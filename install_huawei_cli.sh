#!/bin/bash

echo "🚀 开始强制安装华为云CLI工具..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+')
echo "📋 当前Python版本: $python_version"

if [[ $(echo "$python_version >= 3.6" | bc -l) -eq 0 ]]; then
    echo "❌ Python版本过低，需要3.6+版本"
    exit 1
fi

# 更新pip
echo "📦 更新pip..."
python3 -m pip install --upgrade pip

# 卸载现有版本
echo "🗑️ 卸载现有华为云CLI..."
pip3 uninstall huaweicloudcli -y 2>/dev/null || true

# 清理缓存
echo "🧹 清理pip缓存..."
pip3 cache purge 2>/dev/null || true

# 尝试不同的安装方法
echo "💾 尝试强制安装华为云CLI..."

# 方法1：标准强制安装
if pip3 install --force-reinstall huaweicloudcli; then
    echo "✅ 标准强制安装成功"
elif pip3 install --user --force-reinstall huaweicloudcli; then
    echo "✅ 用户级强制安装成功"
    echo "📝 请将 ~/.local/bin 添加到PATH"
    echo "export PATH=\$PATH:~/.local/bin" >> ~/.bashrc
elif pip3 install --force-reinstall -i https://pypi.tuna.tsinghua.edu.cn/simple huaweicloudcli; then
    echo "✅ 使用清华镜像强制安装成功"
elif sudo pip3 install --force-reinstall huaweicloudcli; then
    echo "✅ 使用sudo强制安装成功"
else
    echo "❌ 所有安装方法都失败了"
    echo "🔧 尝试手动安装方法："
    echo "1. 创建虚拟环境: python3 -m venv huawei_env"
    echo "2. 激活环境: source huawei_env/bin/activate"
    echo "3. 安装: pip install huaweicloudcli"
    exit 1
fi

# 验证安装
echo "🔍 验证安装..."
if command -v hcloud &> /dev/null; then
    echo "✅ 华为云CLI安装成功！"
    echo "📋 版本信息:"
    hcloud --version
    echo ""
    echo "🎯 下一步："
    echo "1. 配置凭证: hcloud configure set"
    echo "2. 测试连接: hcloud ecs list-servers --limit 1"
else
    echo "❌ 安装验证失败，hcloud命令不可用"
    echo "🔧 请检查PATH环境变量"
    exit 1
fi

echo "🎉 华为云CLI强制安装完成！"