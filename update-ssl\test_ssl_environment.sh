#!/bin/bash

echo "=== SSL证书申请环境测试 ==="
echo "测试时间: $(date)"
echo

# 1. 检查WSL环境
echo "1. 检查WSL环境..."
if grep -q Microsoft /proc/version; then
    echo "✅ 运行在WSL环境中"
    echo "   WSL版本信息: $(grep Microsoft /proc/version)"
else
    echo "❌ 不在WSL环境中"
fi
echo

# 2. 检查基本命令
echo "2. 检查基本命令..."
commands=("bash" "certbot" "expect" "openssl" "curl" "wget")
for cmd in "${commands[@]}"; do
    if command -v $cmd >/dev/null 2>&1; then
        echo "✅ $cmd: $(which $cmd)"
    else
        echo "❌ $cmd: 未安装"
    fi
done
echo

# 3. 检查证书申请脚本
echo "3. 检查证书申请脚本..."
scripts=("auto_ssl.sh" "auto_rsa_ssl.sh")
for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        echo "✅ $script: 存在"
        if [ -x "$script" ]; then
            echo "   权限: 可执行"
        else
            echo "   权限: 不可执行，尝试添加权限..."
            chmod +x "$script"
            if [ $? -eq 0 ]; then
                echo "   ✅ 成功添加执行权限"
            else
                echo "   ❌ 无法添加执行权限"
            fi
        fi
        
        # 检查脚本语法
        bash -n "$script" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "   语法: 正确"
        else
            echo "   语法: 有错误"
        fi
    else
        echo "❌ $script: 不存在"
    fi
done
echo

# 4. 检查Let's Encrypt目录
echo "4. 检查Let's Encrypt目录..."
letsencrypt_dirs=("/etc/letsencrypt" "/etc/letsencrypt/live" "/etc/letsencrypt/archive")
for dir in "${letsencrypt_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir: 存在"
        echo "   权限: $(ls -ld $dir | awk '{print $1}')"
        echo "   所有者: $(ls -ld $dir | awk '{print $3":"$4}')"
    else
        echo "❌ $dir: 不存在"
    fi
done
echo

# 5. 检查网络连接
echo "5. 检查网络连接..."
test_urls=("https://acme.freessl.cn" "https://letsencrypt.org" "https://www.google.com")
for url in "${test_urls[@]}"; do
    if curl -s --connect-timeout 5 "$url" >/dev/null; then
        echo "✅ $url: 可访问"
    else
        echo "❌ $url: 无法访问"
    fi
done
echo

# 6. 测试脚本基本功能（不实际申请证书）
echo "6. 测试脚本基本功能..."
for script in "auto_ssl.sh" "auto_rsa_ssl.sh"; do
    if [ -f "$script" ]; then
        echo "测试 $script..."
        # 测试无参数调用
        output=$(bash "$script" 2>&1 | head -3)
        if echo "$output" | grep -q "错误\|用法\|Usage"; then
            echo "   ✅ 参数验证正常"
        else
            echo "   ❌ 参数验证异常"
            echo "   输出: $output"
        fi
    fi
done
echo

# 7. 检查证书目录权限
echo "7. 检查证书同步目录..."
sync_dirs=("/mnt/e/augment/SSL/Certificates" "$HOME/projects/Certificates")
for dir in "${sync_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir: 存在"
        if [ -w "$dir" ]; then
            echo "   权限: 可写"
        else
            echo "   权限: 不可写"
        fi
    else
        echo "❌ $dir: 不存在"
        echo "   尝试创建目录..."
        mkdir -p "$dir" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "   ✅ 成功创建目录"
        else
            echo "   ❌ 无法创建目录"
        fi
    fi
done
echo

# 8. 检查进程和资源
echo "8. 检查系统资源..."
echo "   内存使用: $(free -h | grep Mem | awk '{print $3"/"$2}')"
echo "   磁盘空间: $(df -h / | tail -1 | awk '{print $3"/"$2" ("$5" used)"}')"
echo "   当前用户: $(whoami)"
echo "   用户组: $(groups)"
echo

# 9. 模拟证书申请流程（不实际执行）
echo "9. 模拟证书申请流程..."
test_domain="test-example.com"
echo "   测试域名: $test_domain"

# 检查ECC脚本流程
if [ -f "auto_ssl.sh" ]; then
    echo "   ECC脚本流程检查:"
    echo "     - 域名参数: $test_domain"
    echo "     - 服务器: https://acme.freessl.cn/v2/DV90/directory/ynn8fo1fe1j9uut8istn"
    echo "     - 证书类型: ECC (默认)"
    echo "     - 域名覆盖: *.$test_domain, $test_domain"
fi

# 检查RSA脚本流程
if [ -f "auto_rsa_ssl.sh" ]; then
    echo "   RSA脚本流程检查:"
    echo "     - 域名参数: $test_domain"
    echo "     - 服务器: https://acme.freessl.cn/v2/DV90/directory/ynn8fo1fe1j9uut8istn"
    echo "     - 证书类型: RSA 4096位"
    echo "     - 域名覆盖: *.$test_domain, $test_domain"
fi
echo

# 10. 总结
echo "10. 环境检查总结..."
echo "=== 测试完成 ==="
echo "如果发现问题，请根据上述检查结果进行修复。"
echo "常见问题解决方案:"
echo "  1. WSL环境问题: 重启WSL或更新WSL版本"
echo "  2. 权限问题: sudo chmod +x *.sh"
echo "  3. 网络问题: 检查防火墙和代理设置"
echo "  4. 依赖问题: sudo apt update && sudo apt install certbot expect"
