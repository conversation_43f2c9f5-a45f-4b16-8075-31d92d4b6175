#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Windows SSL证书申请
"""

import subprocess
import os
import requests
import sqlite3
import time

def test_windows_script():
    """测试Windows兼容脚本"""
    print("=== 测试Windows SSL脚本 ===")
    
    git_bash_path = r"C:\Program Files\Git\bin\bash.exe"
    script_path = "../update-ssl/auto_ssl_windows.sh"
    test_domain = "test-windows.example.com"
    
    # 检查脚本是否存在
    if not os.path.exists(script_path):
        print(f"❌ Windows脚本不存在: {script_path}")
        return False
    
    print(f"✅ Windows脚本存在: {script_path}")
    
    # 测试脚本执行
    try:
        print(f"测试域名: {test_domain}")
        
        result = subprocess.run(
            [git_bash_path, script_path, test_domain],
            capture_output=True,
            text=True,
            timeout=15,  # 15秒超时
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"返回码: {result.returncode}")
        
        output = result.stdout + result.stderr
        print(f"输出预览: {output[:500]}")
        
        # 检查是否找到了certbot或使用了WSL
        if 'certbot' in output or 'WSL' in output or '查找certbot' in output:
            print("✅ Windows脚本正常执行")
            return True
        else:
            print("❌ Windows脚本执行异常")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 脚本执行超时（可能正在申请证书）")
        return True
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        return False

def test_kimg_certificate_apply():
    """测试申请kimg.cn的RSA证书"""
    print("\n=== 测试申请kimg.cn RSA证书 ===")
    
    domain = "kimg.cn"
    
    try:
        # 1. 检查SSL管理平台是否运行
        response = requests.get('http://localhost:5000/', timeout=5)
        if response.status_code != 200:
            print("❌ SSL管理平台未运行")
            return False
        
        print("✅ SSL管理平台正在运行")
        
        # 2. 提交证书申请
        print(f"提交 {domain} 的RSA证书申请...")
        
        apply_data = {
            'domain': domain,
            'cert_type': 'rsa'
        }
        
        response = requests.post(
            'http://localhost:5000/apply-certificate',
            data=apply_data,
            allow_redirects=False,
            timeout=10
        )
        
        if response.status_code in [302, 303]:
            print("✅ 证书申请已提交")
            
            # 等待后台处理
            print("等待后台处理...")
            time.sleep(5)
            
            # 3. 检查申请结果
            check_certificate_result(domain)
            
            return True
        else:
            print(f"❌ 证书申请失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到SSL管理平台")
        return False
    except Exception as e:
        print(f"❌ 证书申请测试失败: {e}")
        return False

def check_certificate_result(domain):
    """检查证书申请结果"""
    print(f"\n检查 {domain} 的申请结果...")
    
    try:
        conn = sqlite3.connect('ssl_management.db')
        cursor = conn.cursor()
        
        # 查找最新的申请记录
        cursor.execute('''
            SELECT id, status, error_message, updated_at
            FROM certificates 
            WHERE domain = ? 
            ORDER BY id DESC 
            LIMIT 1
        ''', (domain,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            cert_id, status, error_msg, updated_at = result
            print(f"   证书ID: {cert_id}")
            print(f"   状态: {status}")
            print(f"   更新时间: {updated_at}")
            
            if error_msg:
                print(f"   错误信息: {error_msg}")
                
                # 分析错误类型
                if 'bash: not found' in error_msg:
                    print("   ❌ 仍然有bash not found错误")
                    return False
                elif 'certbot: command not found' in error_msg:
                    print("   ⚠️  certbot未找到，但bash问题已解决")
                    return True
                elif 'Windows脚本' in error_msg or '查找certbot' in error_msg:
                    print("   ✅ 使用了Windows兼容脚本")
                    return True
                else:
                    print("   ⚠️  其他错误，需要进一步调试")
                    return True
            else:
                print("   ✅ 没有错误信息")
                return True
        else:
            print("   ❌ 未找到申请记录")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查结果失败: {e}")
        return False

def check_certbot_installation():
    """检查certbot安装情况"""
    print("\n=== 检查certbot安装 ===")
    
    # 检查可能的certbot路径
    certbot_paths = [
        "certbot",
        "C:\\Python*\\Scripts\\certbot.exe",
        "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python*\\Scripts\\certbot.exe",
        "C:\\Program Files\\Python*\\Scripts\\certbot.exe"
    ]
    
    for path in certbot_paths:
        try:
            if '*' in path:
                # 使用dir命令检查通配符路径
                result = subprocess.run(['dir', path], shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ 找到certbot: {path}")
                    return True
            else:
                # 直接检查命令
                result = subprocess.run([path, '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ certbot可用: {path}")
                    print(f"   版本: {result.stdout.strip()}")
                    return True
        except:
            continue
    
    print("❌ 未找到certbot安装")
    
    # 检查WSL中的certbot
    try:
        result = subprocess.run(['wsl', 'which', 'certbot'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print(f"✅ WSL中找到certbot: {result.stdout.strip()}")
            return True
    except:
        pass
    
    print("❌ WSL中也未找到certbot")
    return False

def main():
    """主函数"""
    print("开始测试Windows SSL证书申请解决方案...\n")
    
    # 1. 检查certbot安装
    certbot_ok = check_certbot_installation()
    
    # 2. 测试Windows脚本
    script_ok = test_windows_script()
    
    # 3. 测试kimg.cn证书申请
    apply_ok = test_kimg_certificate_apply()
    
    print("\n" + "="*50)
    print("测试结果总结:")
    print("="*50)
    
    print(f"Certbot安装: {'✅ 可用' if certbot_ok else '❌ 缺失'}")
    print(f"Windows脚本: {'✅ 正常' if script_ok else '❌ 异常'}")
    print(f"证书申请: {'✅ 成功' if apply_ok else '❌ 失败'}")
    
    if apply_ok:
        print("\n🎉 Windows SSL解决方案成功！")
        print("bash not found问题已解决")
        print("现在可以正常申请SSL证书了")
    else:
        print("\n⚠️  需要进一步调试")
        
    print("\n💡 建议:")
    if not certbot_ok:
        print("1. 安装certbot: pip install certbot")
        print("2. 或确保WSL中有certbot: sudo apt install certbot")
    
    print("3. 重启SSL管理平台服务")
    print("4. 尝试申请kimg.cn的RSA证书")
    print("5. 检查前端是否显示详细错误信息")

if __name__ == "__main__":
    main()
