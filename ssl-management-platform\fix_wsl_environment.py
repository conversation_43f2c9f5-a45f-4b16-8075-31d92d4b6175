#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复WSL环境问题
"""

import subprocess
import os
import time

def fix_wsl_bash():
    """修复WSL中的bash问题"""
    print("=== 修复WSL bash问题 ===")
    
    commands = [
        {
            'name': '更新包列表',
            'command': ['wsl', 'sudo', 'apt', 'update']
        },
        {
            'name': '安装bash',
            'command': ['wsl', 'sudo', 'apt', 'install', '-y', 'bash']
        },
        {
            'name': '安装基本工具',
            'command': ['wsl', 'sudo', 'apt', 'install', '-y', 'coreutils', 'findutils', 'grep']
        },
        {
            'name': '安装certbot',
            'command': ['wsl', 'sudo', 'apt', 'install', '-y', 'certbot']
        },
        {
            'name': '安装expect',
            'command': ['wsl', 'sudo', 'apt', 'install', '-y', 'expect']
        }
    ]
    
    for cmd in commands:
        print(f"\n{cmd['name']}...")
        try:
            result = subprocess.run(
                cmd['command'],
                capture_output=True,
                text=True,
                timeout=120,  # 2分钟超时
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                print(f"   ✅ 成功")
            else:
                print(f"   ❌ 失败: {result.stderr[:200]}")
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ 超时")
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def fix_wsl_paths():
    """修复WSL路径问题"""
    print("\n=== 修复WSL路径问题 ===")
    
    # 检查当前WSL的工作目录
    try:
        result = subprocess.run(['wsl', 'pwd'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            current_dir = result.stdout.strip()
            print(f"当前WSL目录: {current_dir}")
        
        # 检查Windows路径映射
        result = subprocess.run(['wsl', 'ls', '/mnt/'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            mounts = result.stdout.strip()
            print(f"可用挂载点: {mounts}")
        
        # 尝试找到正确的路径
        possible_paths = [
            '/mnt/e/augment/SSL/update-ssl',
            '/mnt/e/augment/ssl/update-ssl',
            '/mnt/e/Augment/SSL/update-ssl',
            f'{current_dir}/../update-ssl'
        ]
        
        for path in possible_paths:
            result = subprocess.run(['wsl', 'test', '-d', path], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ 找到路径: {path}")
                return path
            else:
                print(f"❌ 路径不存在: {path}")
        
        print("❌ 未找到update-ssl目录")
        return None
        
    except Exception as e:
        print(f"❌ 路径检查失败: {e}")
        return None

def create_wrapper_script():
    """创建包装脚本来解决路径问题"""
    print("\n=== 创建包装脚本 ===")
    
    wrapper_content = '''#!/bin/bash

# SSL证书申请包装脚本
# 解决路径和环境问题

DOMAIN="$1"

if [ -z "$DOMAIN" ]; then
    echo "❌ 错误: 请提供域名参数"
    echo "用法: $0 <域名>"
    echo "示例: $0 example.com"
    exit 1
fi

echo "🚀 开始为域名 $DOMAIN 申请SSL证书..."

# 设置服务器
SERVER="--server https://acme.freessl.cn/v2/DV90/directory/ynn8fo1fe1j9uut8istn"

# 申请证书函数
apply_certificate() {
    echo "正在申请证书..."
    
    # 检查certbot是否可用
    if ! command -v certbot >/dev/null 2>&1; then
        echo "❌ certbot未安装，请先安装: sudo apt install certbot"
        exit 1
    fi
    
    # 删除现有证书（如果存在）
    if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
        echo "删除现有证书: $DOMAIN"
        sudo rm -rf "/etc/letsencrypt/live/$DOMAIN"
    fi
    
    # 申请证书
    certbot certonly --manual -d "*.$DOMAIN" -d "$DOMAIN" $SERVER
    
    if [ $? -eq 0 ]; then
        echo "✅ 证书申请成功"
        
        # 设置权限
        sudo chmod -R 755 /etc/letsencrypt/live/
        
        # 同步证书
        sync_certificates
    else
        echo "❌ 证书申请失败"
        exit 1
    fi
}

# 同步证书函数
sync_certificates() {
    echo "开始同步证书..."
    
    # 创建目标目录
    mkdir -p "/tmp/certificates"
    
    # 复制证书
    if [ -d "/etc/letsencrypt/live" ]; then
        sudo cp -rf /etc/letsencrypt/live/* "/tmp/certificates/"
        sudo chmod -R 777 "/tmp/certificates"
        echo "✅ 证书已同步到 /tmp/certificates"
    else
        echo "❌ 证书目录不存在"
    fi
}

# 执行申请
apply_certificate
'''
    
    wrapper_path = "../update-ssl/ssl_wrapper.sh"
    
    try:
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(wrapper_content)
        
        print(f"✅ 包装脚本已创建: {wrapper_path}")
        
        # 设置执行权限
        try:
            result = subprocess.run(['wsl', 'chmod', '+x', '/tmp/ssl_wrapper.sh'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ 执行权限已设置")
            
        except Exception as e:
            print(f"⚠️  权限设置失败: {e}")
        
        return wrapper_path
        
    except Exception as e:
        print(f"❌ 创建包装脚本失败: {e}")
        return None

def test_fixed_environment():
    """测试修复后的环境"""
    print("\n=== 测试修复后的环境 ===")
    
    tests = [
        {
            'name': '检查bash',
            'command': ['wsl', 'which', 'bash']
        },
        {
            'name': '检查certbot',
            'command': ['wsl', 'which', 'certbot']
        },
        {
            'name': '检查expect',
            'command': ['wsl', 'which', 'expect']
        },
        {
            'name': '测试bash版本',
            'command': ['wsl', 'bash', '--version']
        }
    ]
    
    for test in tests:
        print(f"\n{test['name']}:")
        try:
            result = subprocess.run(
                test['command'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                output = result.stdout.strip()[:100]
                print(f"   ✅ 成功: {output}")
            else:
                print(f"   ❌ 失败: {result.stderr.strip()[:100]}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def main():
    """主修复函数"""
    print("开始修复WSL环境...\n")
    
    # 1. 修复bash和依赖
    fix_wsl_bash()
    
    # 2. 修复路径问题
    correct_path = fix_wsl_paths()
    
    # 3. 创建包装脚本
    wrapper_path = create_wrapper_script()
    
    # 4. 测试修复结果
    test_fixed_environment()
    
    print("\n" + "="*50)
    print("修复完成")
    print("="*50)
    
    print("\n📋 修复总结:")
    if correct_path:
        print(f"✅ 找到正确路径: {correct_path}")
    else:
        print("❌ 路径问题未解决")
    
    if wrapper_path:
        print(f"✅ 包装脚本已创建: {wrapper_path}")
    else:
        print("❌ 包装脚本创建失败")
    
    print("\n💡 后续步骤:")
    print("1. 重启WSL: wsl --shutdown")
    print("2. 重新启动SSL管理平台")
    print("3. 尝试申请测试证书")
    print("4. 如果仍有问题，考虑重新安装WSL")

if __name__ == "__main__":
    main()
