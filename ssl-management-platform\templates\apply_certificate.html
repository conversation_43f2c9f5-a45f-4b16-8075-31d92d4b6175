{% extends "base.html" %}

{% block page_title %}申请证书{% endblock %}

{% block page_actions %}
<a href="{{ url_for('certificates') }}" class="btn btn-outline-secondary">
    <i class="fas fa-list"></i>
    查看所有证书
</a>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus-circle"></i>
                    申请新的SSL证书
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="apply-form">
                    <div class="mb-3">
                        <label for="domain" class="form-label">域名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="domain" name="domain"
                               placeholder="例如: example.com" required>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            系统将自动为该域名申请通配符证书 (*.example.com 和 example.com)
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="cert-type" class="form-label">证书类型</label>
                        <select class="form-select" id="cert-type" name="cert_type">
                            <option value="rsa" selected>RSA 证书 (推荐 - 兼容性更好，4096位)</option>
                            <option value="ecc">ECC 证书 (性能更好，体积更小)</option>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            RSA 证书兼容性更好，支持所有云平台。ECC 证书性能更优但部分云平台暂不支持。
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agree-terms" required>
                            <label class="form-check-label" for="agree-terms">
                                我确认域名信息正确，并同意申请SSL证书
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-secondary me-md-2" onclick="history.back()">
                            <i class="fas fa-arrow-left"></i>
                            返回
                        </button>
                        <button type="submit" class="btn btn-primary" id="submit-btn">
                            <i class="fas fa-certificate"></i>
                            申请证书
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- 申请说明 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i>
                    申请说明
                </h6>
            </div>
            <div class="card-body">
                <h6>证书类型</h6>
                <ul class="list-unstyled small">
                    <li><i class="fas fa-check text-success"></i> 免费SSL证书</li>
                    <li><i class="fas fa-check text-success"></i> 支持通配符域名</li>
                    <li><i class="fas fa-check text-success"></i> 90天有效期</li>
                    <li><i class="fas fa-check text-success"></i> 自动续期</li>
                </ul>
                
                <h6 class="mt-3">申请流程</h6>
                <ol class="list-unstyled small">
                    <li><span class="badge bg-primary rounded-pill">1</span> 输入域名</li>
                    <li><span class="badge bg-primary rounded-pill">2</span> 系统验证域名</li>
                    <li><span class="badge bg-primary rounded-pill">3</span> 自动申请证书</li>
                    <li><span class="badge bg-primary rounded-pill">4</span> 生成证书文件</li>
                </ol>
                
                <div class="alert alert-info small">
                    <i class="fas fa-info-circle"></i>
                    <strong>注意：</strong>申请过程可能需要几分钟时间，请耐心等待。
                </div>
            </div>
        </div>
        
        <!-- 最近申请记录 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history"></i>
                    最近申请
                </h6>
            </div>
            <div class="card-body">
                <div id="recent-applications">
                    <p class="text-muted small">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 申请进度模态框 -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin"></i>
                    正在申请证书
                </h5>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">申请中...</span>
                    </div>
                    <p class="mt-3">正在为域名申请SSL证书，请稍候...</p>
                    <small class="text-muted">此过程可能需要几分钟时间</small>
                </div>
                
                <div class="mt-4">
                    <h6>申请步骤：</h6>
                    <ul class="list-unstyled">
                        <li id="step-1"><i class="fas fa-spinner fa-spin text-primary"></i> 验证域名...</li>
                        <li id="step-2"><i class="fas fa-clock text-muted"></i> 生成证书请求...</li>
                        <li id="step-3"><i class="fas fa-clock text-muted"></i> 获取证书...</li>
                        <li id="step-4"><i class="fas fa-clock text-muted"></i> 保存证书文件...</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <small class="text-muted">请不要关闭此窗口</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('apply-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const domain = document.getElementById('domain').value.trim();
    const agreeTerms = document.getElementById('agree-terms').checked;
    
    if (!domain) {
        alert('请输入域名');
        return;
    }
    
    if (!agreeTerms) {
        alert('请同意申请条款');
        return;
    }
    
    // 验证域名格式
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(domain)) {
        alert('请输入有效的域名格式');
        return;
    }
    
    // 显示进度模态框
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // 模拟申请步骤进度
    setTimeout(() => {
        updateStep(1, 'success', '域名验证完成');
        updateStep(2, 'loading', '生成证书请求...');
    }, 2000);
    
    setTimeout(() => {
        updateStep(2, 'success', '证书请求已生成');
        updateStep(3, 'loading', '获取证书...');
    }, 4000);
    
    setTimeout(() => {
        updateStep(3, 'success', '证书获取完成');
        updateStep(4, 'loading', '保存证书文件...');
    }, 6000);
    
    // 提交表单
    setTimeout(() => {
        this.submit();
    }, 1000);
});

function updateStep(stepNum, status, text) {
    const stepElement = document.getElementById(`step-${stepNum}`);
    const icon = stepElement.querySelector('i');
    
    icon.className = '';
    
    switch(status) {
        case 'loading':
            icon.className = 'fas fa-spinner fa-spin text-primary';
            break;
        case 'success':
            icon.className = 'fas fa-check text-success';
            break;
        case 'error':
            icon.className = 'fas fa-times text-danger';
            break;
        default:
            icon.className = 'fas fa-clock text-muted';
    }
    
    stepElement.innerHTML = icon.outerHTML + ' ' + text;
}

// 加载最近申请记录
function loadRecentApplications() {
    // 这里可以通过AJAX加载最近的申请记录
    const recentDiv = document.getElementById('recent-applications');
    recentDiv.innerHTML = `
        <p class="text-muted small">暂无最近申请记录</p>
    `;
}

document.addEventListener('DOMContentLoaded', function() {
    loadRecentApplications();
});

// 域名输入验证
document.getElementById('domain').addEventListener('input', function() {
    const domain = this.value.trim();
    const submitBtn = document.getElementById('submit-btn');
    
    if (domain) {
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
        if (domainRegex.test(domain)) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        }
    } else {
        this.classList.remove('is-valid', 'is-invalid');
    }
});
</script>
{% endblock %}
