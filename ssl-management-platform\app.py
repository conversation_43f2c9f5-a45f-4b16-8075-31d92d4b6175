#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL证书管理平台
主应用文件
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
import os
import sys
import sqlite3
import subprocess
import json
from datetime import datetime
import threading
import time
import logging
import re
from logging.handlers import RotatingFileHandler

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 添加ssl-update目录到Python路径
ssl_update_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'ssl-update')
sys.path.append(ssl_update_path)

# 导入配置
from config import config

# 导入数据处理库
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
    print(f"pandas导入成功，版本: {pd.__version__}")
except ImportError:
    PANDAS_AVAILABLE = False
    print("pandas未安装，无法处理Excel文件")

def create_app(config_name=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    config_name = config_name or os.environ.get('FLASK_ENV', 'default')
    app.config.from_object(config[config_name])

    # 确保必要目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)

    # 配置日志
    if not app.debug and not app.testing:
        file_handler = RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=10240000,
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('SSL管理平台启动')

    return app

# 创建应用实例
app = create_app()

# 使用 config.py 中的配置
from config import Config

# 数据库初始化
def init_db():
    """初始化数据库"""
    conn = sqlite3.connect(Config.DATABASE)
    cursor = conn.cursor()
    
    # 证书记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS certificates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            domain TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            cert_path TEXT,
            error_message TEXT,
            output_log TEXT
        )
    ''')

    # 检查并添加缺失的字段（数据库迁移）
    try:
        cursor.execute("SELECT output_log FROM certificates LIMIT 1")
    except sqlite3.OperationalError:
        # output_log字段不存在，添加它
        cursor.execute("ALTER TABLE certificates ADD COLUMN output_log TEXT")
        print("数据库迁移：添加 output_log 字段到 certificates 表")

    # 云平台更新状态表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS platform_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            certificate_id INTEGER,
            platform_id TEXT NOT NULL,
            platform_name TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            FOREIGN KEY (certificate_id) REFERENCES certificates (id)
        )
    ''')
    
    # 域名检查记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS domain_checks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            domain TEXT NOT NULL,
            dns_record TEXT,
            ssl_status TEXT,
            expiry_date TEXT,
            checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            platform_detected TEXT,
            batch_id TEXT
        )
    ''')

    # 添加batch_id列（如果不存在）
    try:
        cursor.execute('ALTER TABLE domain_checks ADD COLUMN batch_id TEXT')
    except sqlite3.OperationalError:
        # 列已存在，忽略错误
        pass

    # 添加dns_file_id列（如果不存在）
    try:
        cursor.execute('ALTER TABLE domain_checks ADD COLUMN dns_file_id INTEGER')
    except sqlite3.OperationalError:
        # 列已存在，忽略错误
        pass

    # 检查任务表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS check_tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            batch_id TEXT UNIQUE NOT NULL,
            dns_file_id INTEGER NOT NULL,
            total_domains INTEGER NOT NULL,
            completed_domains INTEGER DEFAULT 0,
            status TEXT DEFAULT 'running',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # DNS记录上传表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS dns_uploads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            platform TEXT NOT NULL,
            uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            domains_count INTEGER DEFAULT 0,
            file_path TEXT
        )
    ''')

    # 腾讯云证书缓存表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tencent_certificates_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cert_id TEXT UNIQUE NOT NULL,
            domain TEXT NOT NULL,
            cert_begin_time TEXT,
            cert_end_time TEXT,
            status TEXT,
            status_name TEXT,
            alias TEXT,
            subject_alt_name TEXT,
            tags TEXT,
            is_expiring BOOLEAN DEFAULT 0,
            insert_time TEXT,
            certificate_type TEXT DEFAULT 'SVR',
            days_remaining INTEGER,
            cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    conn.commit()
    conn.close()

# DNS文件解析功能
def parse_dns_file(file_path, platform):
    """解析DNS文件，提取域名

    支持CSV、TXT和Excel格式，针对不同云平台优化解析逻辑
    返回域名列表和解析统计信息
    """
    domains = []
    parse_stats = {
        'total_records': 0,
        'valid_domains': 0,
        'skipped_records': 0,
        'parsed_records': 0,
        'errors': []
    }

    file_ext = os.path.splitext(file_path)[1].lower()

    try:
        # Excel文件处理
        if file_ext in ['.xls', '.xlsx']:
            domains, parse_stats = _parse_excel_dns_file(file_path, platform)

        # CSV或TXT文件处理
        elif file_ext in ['.csv', '.txt']:
            domains, parse_stats = _parse_csv_dns_file(file_path, platform)

        else:
            parse_stats['errors'].append(f"不支持的文件格式: {file_ext}")
            return [], parse_stats

        # 域名去重和验证
        unique_domains = []
        seen_domains = set()

        for domain in domains:
            domain_clean = _clean_domain(domain)
            if domain_clean and _is_valid_domain(domain_clean) and domain_clean not in seen_domains:
                unique_domains.append(domain_clean)
                seen_domains.add(domain_clean)

        parse_stats['valid_domains'] = len(unique_domains)

        # 限制域名数量，避免过多域名影响性能
        if len(unique_domains) > 500:
            parse_stats['errors'].append(f"域名数量过多({len(unique_domains)})，已限制为前500个")
            unique_domains = unique_domains[:500]

        logging.info(f"DNS文件解析完成: {parse_stats}")
        return unique_domains, parse_stats

    except Exception as e:
        error_msg = f"解析DNS文件失败: {str(e)}"
        logging.error(error_msg)
        parse_stats['errors'].append(error_msg)
        return [], parse_stats


def _parse_excel_dns_file(file_path, platform):
    """解析Excel格式的DNS文件"""
    domains = []
    parse_stats = {'total_records': 0, 'valid_domains': 0, 'skipped_records': 0, 'parsed_records': 0, 'errors': []}

    try:
        import pandas as pd
        df = pd.read_excel(file_path)
        parse_stats['total_records'] = len(df)

        # 将文件路径传递给DataFrame，以便解析函数使用
        df._file_path = file_path

        # 根据平台类型使用不同的解析策略
        if platform == 'tencent':
            domains = _parse_tencent_dns_records(df, parse_stats)
        elif platform == 'aliyun':
            domains = _parse_aliyun_dns_records(df, parse_stats)
        else:
            domains = _parse_generic_dns_records(df, parse_stats)

    except ImportError:
        error_msg = "pandas未安装，无法处理Excel文件"
        logging.warning(error_msg)
        parse_stats['errors'].append(error_msg)
    except Exception as e:
        error_msg = f"Excel文件解析错误: {str(e)}"
        logging.error(error_msg)
        parse_stats['errors'].append(error_msg)

    return domains, parse_stats


def _parse_csv_dns_file(file_path, platform):
    """解析CSV格式的DNS文件"""
    domains = []
    parse_stats = {'total_records': 0, 'valid_domains': 0, 'skipped_records': 0, 'parsed_records': 0, 'errors': []}

    try:
        import csv

        # 尝试不同的编码格式
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        df_data = None

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                    reader = csv.reader(f)
                    rows = list(reader)
                    if rows:
                        df_data = rows
                        break
            except UnicodeDecodeError:
                continue

        if not df_data:
            parse_stats['errors'].append("无法读取CSV文件，编码格式不支持")
            return domains, parse_stats

        parse_stats['total_records'] = len(df_data) - 1 if len(df_data) > 1 else 0

        # 转换为DataFrame格式以便统一处理
        if len(df_data) > 1:
            try:
                import pandas as pd
                df = pd.DataFrame(df_data[1:], columns=df_data[0])
            except ImportError:
                # 如果pandas不可用，使用简单的解析逻辑
                for row in df_data[1:]:
                    if len(row) > 0 and '.' in str(row[0]):
                        potential_domain = str(row[0]).strip()
                        if _is_valid_domain(potential_domain):
                            domains.append(potential_domain)
                return domains, parse_stats

            # 根据平台类型使用不同的解析策略
            if platform == 'tencent':
                domains = _parse_tencent_dns_records(df, parse_stats)
            elif platform == 'aliyun':
                domains = _parse_aliyun_dns_records(df, parse_stats)
            else:
                domains = _parse_generic_dns_records(df, parse_stats)

    except Exception as e:
        error_msg = f"CSV文件解析错误: {str(e)}"
        logging.error(error_msg)
        parse_stats['errors'].append(error_msg)

    return domains, parse_stats


def _parse_tencent_dns_records(df, parse_stats):
    """解析腾讯云DNS记录 - 兼容API下载和手动下载两种格式"""
    domains = []
    
    # 腾讯云DNS记录的常见列名（兼容多种格式）
    host_columns = ['主机记录', 'host', '主机', '记录名']
    value_columns = ['记录值', 'value', '值', '记录内容']
    
    try:
        # 打印列名用于调试
        print(f"[DEBUG] 腾讯云DNS文件列名: {list(df.columns)}")
        
        # 查找主机记录列
        host_col = None
        for col in host_columns:
            if col in df.columns:
                host_col = col
                break
        
        if host_col is None:
            # 如果找不到标准列名，尝试查找包含关键字的列
            for col in df.columns:
                if any(keyword in str(col) for keyword in ['主机', 'host', '记录']):
                    host_col = col
                    print(f"[DEBUG] 找到主机记录列: {host_col}")
                    break
        
        if host_col is None:
            error_msg = f"未找到主机记录列，可用列名: {list(df.columns)}"
            parse_stats['errors'].append(error_msg)
            print(f"[ERROR] {error_msg}")
            return domains
        
        # 查找记录值列
        value_col = None
        for col in value_columns:
            if col in df.columns:
                value_col = col
                break
        
        if value_col is None:
            # 如果找不到标准列名，尝试查找包含关键字的列
            for col in df.columns:
                if any(keyword in str(col) for keyword in ['记录值', 'value', '值', '内容']):
                    value_col = col
                    print(f"[DEBUG] 找到记录值列: {value_col}")
                    break
        
        if value_col is None:
            error_msg = f"未找到记录值列，可用列名: {list(df.columns)}"
            parse_stats['errors'].append(error_msg)
            print(f"[ERROR] {error_msg}")
            return domains
        
        print(f"[DEBUG] 使用主机记录列: {host_col}, 记录值列: {value_col}")
        
        # 解析每一行
        for index, row in df.iterrows():
            try:
                # 获取主机记录和记录值
                host = str(row[host_col]).strip() if pd.notna(row[host_col]) else ""
                value = str(row[value_col]).strip() if pd.notna(row[value_col]) else ""
                
                # 跳过空值和无效记录
                if not host or not value or host == 'nan' or value == 'nan':
                    parse_stats['skipped_records'] += 1
                    continue
                
                # 获取记录类型（如果有的话），但不强制要求
                record_type = ""
                type_columns = ['记录类型', 'type', '类型', 'Type']
                for type_col in type_columns:
                    if type_col in df.columns and pd.notna(row[type_col]):
                        record_type = str(row[type_col]).strip().upper()
                        break

                # 如果有记录类型信息，可以用于过滤，但不是必需的
                # 因为用户说只需要主机记录和记录值就行了
                # 这里暂时不过滤任何记录类型，让用户在SSL检查时自己判断
                
                # 尝试从文件名或其他方式获取根域名
                # 这里需要根据实际情况调整
                root_domain = None
                
                # 如果有域名列，直接使用
                domain_columns = ['域名', 'domain', '根域名', 'DomainName']
                for domain_col in domain_columns:
                    if domain_col in df.columns and pd.notna(row[domain_col]):
                        root_domain = str(row[domain_col]).strip()
                        break
                
                # 如果没有域名列，尝试从文件名推断
                if not root_domain:
                    # 尝试从DataFrame的文件路径属性中获取
                    if hasattr(df, '_file_path') and df._file_path:
                        import os
                        filename = os.path.basename(df._file_path)
                        # 从文件名中提取域名（去掉扩展名）
                        filename_without_ext = os.path.splitext(filename)[0]

                        # 去掉时间戳前缀（格式：YYYYMMDD_HHMMSS_）
                        import re
                        # 匹配时间戳格式：8位数字_6位数字_
                        timestamp_pattern = r'^\d{8}_\d{6}_'
                        root_domain = re.sub(timestamp_pattern, '', filename_without_ext)

                        print(f"[DEBUG] 原始文件名: {filename}")
                        print(f"[DEBUG] 去掉时间戳后的根域名: {root_domain}")
                    else:
                        # 可以从全局变量或参数中获取
                        root_domain = getattr(_parse_tencent_dns_records, 'current_domain', None)
                
                # 构建完整域名
                if host == '@':
                    full_domain = root_domain
                elif root_domain:
                    full_domain = f"{host}.{root_domain}"
                else:
                    # 如果无法确定根域名，跳过这条记录
                    parse_stats['skipped_records'] += 1
                    continue
                
                if full_domain:
                    domains.append({
                        'domain': full_domain,
                        'host': host,
                        'value': value,
                        'source': 'tencent_dns',
                        'record_type': record_type
                    })
                    parse_stats['parsed_records'] += 1
                    print(f"[DEBUG] 解析成功: {host} -> {value} ({full_domain})")
                else:
                    parse_stats['skipped_records'] += 1
                    
            except Exception as e:
                parse_stats['skipped_records'] += 1
                print(f"[ERROR] 解析腾讯云DNS记录行失败: {str(e)}")
                
    except Exception as e:
        error_msg = f"解析腾讯云DNS记录失败: {str(e)}"
        parse_stats['errors'].append(error_msg)
        print(f"[ERROR] {error_msg}")
    
    print(f"[DEBUG] 腾讯云DNS解析完成，共解析 {len(domains)} 条记录")

    # 转换为字符串格式以兼容现有系统
    domain_strings = []
    for domain_info in domains:
        if isinstance(domain_info, dict):
            domain_strings.append(domain_info.get('domain', ''))
        else:
            domain_strings.append(str(domain_info))

    # 更新统计信息
    parse_stats['valid_domains'] = len(domain_strings)

    return domain_strings




def _parse_generic_dns_records(df, parse_stats):
    """解析通用格式的DNS记录"""
    domains = []

    # 通用列名匹配
    possible_domain_columns = []

    for col in df.columns:
        col_str = str(col).lower()
        # 查找可能包含域名的列
        if any(keyword in col_str for keyword in ['domain', '域名', 'host', '主机', 'name', '名称']):
            possible_domain_columns.append(col)

    # 如果没有找到明显的域名列，尝试从所有列中提取
    if not possible_domain_columns:
        possible_domain_columns = list(df.columns)

    for index, row in df.iterrows():
        for col in possible_domain_columns:
            try:
                value = str(row[col]).strip()
                if value and value != 'nan' and '.' in value:
                    # 简单的域名格式检查
                    if _is_potential_domain(value):
                        domains.append(value)
            except:
                continue

    return domains


def _find_column(df, possible_names):
    """在DataFrame中查找匹配的列名"""
    for col in df.columns:
        col_str = str(col).lower()
        for name in possible_names:
            if name.lower() in col_str or col_str in name.lower():
                return col
    return None


def _clean_domain(domain):
    """清理域名字符串"""
    if not domain:
        return None

    domain = str(domain).strip().lower()

    # 移除协议前缀
    if domain.startswith(('http://', 'https://')):
        domain = domain.split('://', 1)[1]

    # 移除路径
    if '/' in domain:
        domain = domain.split('/')[0]

    # 移除端口号
    if ':' in domain and not domain.count(':') > 1:  # 避免IPv6地址
        domain = domain.split(':')[0]

    # 移除末尾的点
    domain = domain.rstrip('.')

    return domain if domain else None


def _is_valid_domain(domain):
    """验证域名格式是否有效"""
    if not domain or len(domain) > 253:
        return False

    # 基本格式检查
    if not '.' in domain:
        return False

    parts = domain.split('.')
    if len(parts) < 2:
        return False

    # 检查每个部分
    for part in parts:
        if not part or len(part) > 63:
            return False
        if not part.replace('-', '').replace('_', '').isalnum():
            return False
        if part.startswith('-') or part.endswith('-'):
            return False

    # 排除明显不是域名的内容
    invalid_patterns = ['localhost', '127.0.0.1', '0.0.0.0', 'example.com', 'test.com']
    if domain in invalid_patterns:
        return False

    # 检查顶级域名
    tld = parts[-1]
    if not tld.isalpha() or len(tld) < 2:
        return False

    return True


def _is_potential_domain(value):
    """检查字符串是否可能是域名"""
    if not value or len(value) > 253:
        return False

    # 必须包含点
    if '.' not in value:
        return False

    # 不能包含空格或特殊字符
    if any(char in value for char in [' ', '\t', '\n', '\r', '\\', '/', '?', '#']):
        return False

    # 简单的格式检查
    parts = value.split('.')
    if len(parts) < 2:
        return False

    return True

# SSL证书检查功能 - 简化版本（基于原始工具）
def check_ssl_certificate(domain):
    """检查SSL证书状态 - 使用优化的简化方法（基于原始工具）"""
    result = {
        'domain': domain,
        'has_ssl': False,
        'issuer': '',
        'issuer_org': '',
        'subject': '',
        'valid_from': '',
        'valid_to': '',
        'days_remaining': 0,
        'status': 'unknown',
        'san_domains': [],
        'signature_algorithm': 'unknown',
        'key_size': 0,
        'serial_number': '',
        'fingerprint': '',
        'is_wildcard': False,
        'is_self_signed': False,
        'cert_chain_length': 1,
        'error': None
    }

    try:
        import socket
        import ssl
        import hashlib
        from datetime import datetime

        # 使用原始工具的简化方法
        context = ssl.create_default_context()
        conn = context.wrap_socket(socket.socket(socket.AF_INET), server_hostname=domain)

        # 4秒超时
        conn.settimeout(4.0)

        # 连接到服务器
        conn.connect((domain, 443))

        # 获取证书
        cert = conn.getpeercert()

        if cert:
            result['has_ssl'] = True

            # 提取过期日期
            expiry_date = datetime.strptime(cert['notAfter'], "%b %d %H:%M:%S %Y %Z")
            result['valid_to'] = expiry_date.strftime('%Y-%m-%d %H:%M:%S')

            # 提取生效日期
            if 'notBefore' in cert:
                start_date = datetime.strptime(cert['notBefore'], "%b %d %H:%M:%S %Y %Z")
                result['valid_from'] = start_date.strftime('%Y-%m-%d %H:%M:%S')

            # 计算剩余天数
            days_remaining = (expiry_date - datetime.now()).days
            result['days_remaining'] = days_remaining

            # 基本证书信息
            issuer = dict(x[0] for x in cert.get('issuer', []))
            subject = dict(x[0] for x in cert.get('subject', []))

            result['issuer'] = issuer.get('commonName', '')
            result['issuer_org'] = issuer.get('organizationName', '')
            result['subject'] = subject.get('commonName', domain)

            # 序列号
            result['serial_number'] = cert.get('serialNumber', '')

            # SAN域名
            san_list = []
            for san_type, san_value in cert.get('subjectAltName', []):
                if san_type == 'DNS':
                    san_list.append(san_value)
            result['san_domains'] = san_list

            # 检查是否为通配符证书
            all_domains = [result['subject']] + san_list
            result['is_wildcard'] = any(d.startswith('*.') for d in all_domains)

            # 检查是否为自签名证书
            result['is_self_signed'] = (
                issuer.get('commonName') == subject.get('commonName') and
                issuer.get('organizationName') == subject.get('organizationName')
            )

            # 确定状态
            if days_remaining <= 0:
                result['status'] = 'expired'
            elif days_remaining <= 30:
                result['status'] = 'warning'
            else:
                result['status'] = 'valid'
        else:
            result['status'] = 'no_certificate'
            result['error'] = 'No certificate found'

        conn.close()

    except socket.timeout:
        result['status'] = 'timeout'
        result['error'] = '连接超时'
    except socket.gaierror as e:
        result['status'] = 'dns_error'
        result['error'] = f'DNS解析失败: {str(e)}'
    except ssl.SSLError as e:
        result['status'] = 'ssl_error'
        result['error'] = f'SSL错误: {str(e)}'
    except ConnectionRefusedError:
        result['status'] = 'connection_refused'
        result['error'] = '连接被拒绝（端口443可能未开放）'
    except Exception as e:
        result['status'] = 'error'
        result['error'] = str(e)
        logging.error(f"SSL检查失败 ({domain}): {str(e)}")

    return result

def find_dns_record_for_domain(domain):
    """查找域名对应的DNS记录值"""
    try:
        # 使用ssl_checker_integration模块的功能
        from ssl_checker_integration import SSLCheckerIntegration

        checker = SSLCheckerIntegration()
        dns_record = checker._find_dns_record_for_domain(domain)

        return dns_record

    except Exception as e:
        logging.error(f"查找DNS记录失败: {str(e)}")
        return None

def find_dns_record_from_file(domain, dns_file_id):
    """从指定的DNS文件中查找域名对应的DNS记录值"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取DNS文件信息
        cursor.execute('SELECT file_path, platform FROM dns_uploads WHERE id = ?', (dns_file_id,))
        file_info = cursor.fetchone()
        conn.close()

        if not file_info:
            return None

        file_path, platform = file_info

        if not file_path or not os.path.exists(file_path):
            return None

        # 解析DNS文件查找记录值
        return extract_dns_record_from_file(file_path, platform, domain)

    except Exception as e:
        logging.error(f"从DNS文件查找记录失败: {str(e)}")
        return None

def extract_dns_record_from_file(file_path, platform, target_domain):
    """从DNS文件中提取指定域名的记录值"""
    try:
        import pandas as pd

        # 读取文件
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path)
        elif file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            return None

        # 根据平台类型查找记录值
        if platform == 'tencent':
            return find_tencent_dns_record(df, target_domain)
        elif platform == 'aliyun':
            return find_aliyun_dns_record(df, target_domain)
        else:
            return find_generic_dns_record(df, target_domain)

    except Exception as e:
        logging.error(f"从DNS文件提取记录失败: {e}")
        return None

def find_tencent_dns_record(df, target_domain):
    """从腾讯云DNS文件中查找记录值 - 兼容多种格式"""
    try:
        print(f"[DEBUG] 查找腾讯云DNS记录: {target_domain}")
        print(f"[DEBUG] 文件列名: {list(df.columns)}")
        
        # 腾讯云DNS记录的常见列名（兼容多种格式）
        host_columns = ['主机记录', 'host', '主机', '记录名']
        value_columns = ['记录值', 'value', '值', '记录内容']
        
        # 查找主机记录列
        host_col = None
        for col in host_columns:
            if col in df.columns:
                host_col = col
                break
        
        if host_col is None:
            # 尝试模糊匹配
            for col in df.columns:
                if any(keyword in str(col) for keyword in ['主机', 'host', '记录']):
                    host_col = col
                    break
        
        # 查找记录值列
        value_col = None
        for col in value_columns:
            if col in df.columns:
                value_col = col
                break
        
        if value_col is None:
            # 尝试模糊匹配
            for col in df.columns:
                if any(keyword in str(col) for keyword in ['记录值', 'value', '值', '内容']):
                    value_col = col
                    break
        
        if not host_col or not value_col:
            print(f"[ERROR] 未找到必要的列: host_col={host_col}, value_col={value_col}")
            return None
        
        print(f"[DEBUG] 使用列: {host_col} -> {value_col}")
        
        # 解析目标域名
        parts = target_domain.split('.')
        if len(parts) < 2:
            return None
        
        # 提取主机记录部分
        if len(parts) == 2:
            # 根域名，查找 @ 记录
            target_host = '@'
        else:
            # 子域名，提取主机记录
            target_host = '.'.join(parts[:-2]) if len(parts) > 3 else parts[0]
        
        print(f"[DEBUG] 查找主机记录: {target_host}")
        
        # 在DataFrame中查找匹配的记录
        for index, row in df.iterrows():
            try:
                host = str(row[host_col]).strip() if pd.notna(row[host_col]) else ""
                value = str(row[value_col]).strip() if pd.notna(row[value_col]) else ""
                
                if host == target_host and value:
                    print(f"[DEBUG] 找到匹配记录: {host} -> {value}")
                    return value
                    
            except Exception as e:
                print(f"[ERROR] 处理行时出错: {str(e)}")
                continue
        
        print(f"[DEBUG] 未找到匹配的记录")
        return None
        
    except Exception as e:
        print(f"[ERROR] 从腾讯云DNS文件查找记录失败: {e}")
        return None

        
def find_aliyun_dns_record(df, target_domain):
    """从阿里云DNS文件中查找记录值"""
    # 类似腾讯云的实现
    return find_tencent_dns_record(df, target_domain)

def find_generic_dns_record(df, target_domain):
    """从通用DNS文件中查找记录值"""
    # 类似腾讯云的实现
    return find_tencent_dns_record(df, target_domain)

def check_certificates_task(upload_id, domains):
    """后台任务：检查证书状态"""
    try:
        # 使用SSL检查集成模块
        from ssl_checker_integration import async_batch_check

        logging.info(f"开始检查 {len(domains)} 个域名的SSL证书状态")

        # 异步批量检查
        results = async_batch_check(domains, upload_id, Config.DATABASE)

        # 统计结果
        valid_count = sum(1 for r in results if r.get('status') == 'valid')
        warning_count = sum(1 for r in results if r.get('status') in ['warning', 'critical'])
        expired_count = sum(1 for r in results if r.get('status') == 'expired')
        error_count = sum(1 for r in results if r.get('status') in ['error', 'timeout', 'dns_error', 'ssl_error'])

        logging.info(f"证书检查任务完成: 总计={len(results)}, 正常={valid_count}, "
                    f"警告={warning_count}, 过期={expired_count}, 错误={error_count}")

    except Exception as e:
        logging.error(f"证书检查任务失败: {str(e)}")

# 路由定义
@app.route('/')
def index():
    """主页"""
    return render_template('index.html', platforms=Config.CLOUD_PLATFORMS)

@app.route('/certificates')
def certificates():
    """证书管理页面"""
    conn = sqlite3.connect(Config.DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, domain, status, created_at, updated_at, error_message, output_log
        FROM certificates
        ORDER BY created_at DESC
    ''')
    
    certs = cursor.fetchall()
    conn.close()
    
    return render_template('certificates.html', certificates=certs)

@app.route('/apply-certificate', methods=['GET', 'POST'])
def apply_certificate():
    """申请证书页面"""
    if request.method == 'POST':
        domain = request.form.get('domain', '').strip()
        cert_type = request.form.get('cert_type', 'rsa').strip()

        if not domain:
            flash('请输入域名', 'error')
            return redirect(url_for('apply_certificate'))

        # 保存到数据库
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO certificates (domain, status, error_message) VALUES (?, ?, ?)',
            (domain, 'applying', f'证书类型: {cert_type.upper()}')
        )
        cert_id = cursor.lastrowid
        conn.commit()
        conn.close()

        # 异步执行证书申请，传递证书类型
        thread = threading.Thread(target=apply_ssl_certificate, args=(cert_id, domain, cert_type))
        thread.daemon = True
        thread.start()

        cert_type_name = 'ECC' if cert_type == 'ecc' else 'RSA'
        flash(f'{cert_type_name} 证书申请已开始，域名: {domain}', 'success')
        return redirect(url_for('certificates'))
    
    return render_template('apply_certificate.html')

@app.route('/upload-certificate', methods=['GET', 'POST'])
def upload_certificate():
    """上传证书页面"""
    if request.method == 'POST':
        print(f"[DEBUG] 收到POST请求，表单数据: {dict(request.form)}")
        upload_type = request.form.get('upload_type', 'auto')
        print(f"[DEBUG] upload_type: {upload_type}")

        if upload_type == 'auto':
            cert_id = request.form.get('certificate_id')
            if not cert_id:
                flash('请选择证书', 'error')
                return redirect(url_for('upload_certificate'))

            # 获取选中的云平台
            platforms = {
                'huawei': request.form.get('platform-huawei') == 'on',
                'tencent': request.form.get('platform-tencent') == 'on',
                'volcengine': request.form.get('platform-volcengine') == 'on',
                'aliyun': request.form.get('platform-aliyun') == 'on'
            }

            # 异步执行证书上传
            thread = threading.Thread(target=upload_ssl_certificate, args=(cert_id, 'auto', None, platforms))
            thread.daemon = True
            thread.start()

            flash('证书上传已开始', 'success')

        elif upload_type == 'custom':
            print(f"[DEBUG] 处理自定义证书上传请求")
            print(f"[DEBUG] 表单数据: {dict(request.form)}")

            cert_folder = request.form.get('cert_folder')
            print(f"[DEBUG] cert_folder: {cert_folder}")

            if not cert_folder:
                flash('请选择证书文件夹', 'error')
                return redirect(url_for('upload_certificate'))

            # 获取选中的云平台
            platforms = {
                'huawei': request.form.get('custom-platform-huawei') == 'on',
                'tencent': request.form.get('custom-platform-tencent') == 'on',
                'volcengine': request.form.get('custom-platform-volcengine') == 'on',
                'aliyun': request.form.get('custom-platform-aliyun') == 'on'
            }
            print(f"[DEBUG] 平台选择: {platforms}")
            print(f"[DEBUG] 表单中的平台字段:")
            for key in ['custom-platform-huawei', 'custom-platform-tencent', 'custom-platform-volcengine', 'custom-platform-aliyun']:
                value = request.form.get(key)
                print(f"[DEBUG]   {key}: {value}")

            # 先创建证书记录，显示上传中状态
            conn = sqlite3.connect(Config.DATABASE)
            cursor = conn.cursor()

            # 尝试从证书文件夹获取域名信息
            try:
                cert_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'Certificates')
                cert_dir = os.path.abspath(cert_dir)
                folder_path = os.path.join(cert_dir, cert_folder)

                # 查找证书文件并提取域名
                domain = cert_folder  # 默认使用文件夹名
                print(f"[DEBUG] 处理文件夹: {cert_folder}")
                print(f"[DEBUG] 文件夹路径: {folder_path}")

                for file in os.listdir(folder_path):
                    if file.endswith(('.crt', '.pem')) and 'key' not in file.lower():
                        cert_file_path = os.path.join(folder_path, file)
                        print(f"[DEBUG] 找到证书文件: {file}")
                        try:
                            # 使用openssl提取域名
                            result = subprocess.run(
                                ['openssl', 'x509', '-in', cert_file_path, '-text', '-noout'],
                                capture_output=True,
                                text=True,
                                timeout=10
                            )
                            print(f"[DEBUG] openssl返回码: {result.returncode}")

                            if result.returncode == 0:
                                # 从Subject Alternative Name中提取域名
                                for line in result.stdout.split('\n'):
                                    if 'DNS:' in line:
                                        print(f"[DEBUG] 找到SAN行: {line.strip()}")
                                        dns_entries = line.split('DNS:')[1:]
                                        if dns_entries:
                                            extracted_domain = dns_entries[0].split(',')[0].strip()
                                            print(f"[DEBUG] 提取的域名: {extracted_domain}")
                                            domain = extracted_domain
                                            break

                                # 如果没有SAN，从Subject CN中提取
                                if domain == cert_folder:
                                    for line in result.stdout.split('\n'):
                                        if 'Subject:' in line and 'CN=' in line:
                                            print(f"[DEBUG] 找到Subject行: {line.strip()}")
                                            cn_part = line.split('CN=')[1].split(',')[0].strip()
                                            print(f"[DEBUG] 提取的CN域名: {cn_part}")
                                            domain = cn_part
                                            break
                            else:
                                print(f"[DEBUG] openssl错误: {result.stderr}")
                        except Exception as e:
                            print(f"[DEBUG] openssl异常: {e}")
                            # 如果openssl失败，尝试从文件名提取域名
                            if 'STAR_' in file:
                                # 处理通配符证书文件名，如 STAR_cmcmcdn_com_integrated.crt
                                domain_part = file.replace('STAR_', '').replace('_integrated.crt', '').replace('_bundle.crt', '').replace('.crt', '').replace('.pem', '')
                                domain = domain_part.replace('_', '.')
                                print(f"[DEBUG] 从文件名提取域名: {domain}")
                            elif '_' in file and '.' in file:
                                # 处理其他格式的文件名
                                domain_part = file.split('.')[0]  # 去掉扩展名
                                if '_' in domain_part:
                                    # 可能是 domain_com_bundle 格式
                                    parts = domain_part.split('_')
                                    if len(parts) >= 2:
                                        domain = '.'.join(parts[:-1]) if parts[-1] in ['bundle', 'integrated', 'cert'] else '.'.join(parts)
                                        domain = domain.replace('_', '.')
                                        print(f"[DEBUG] 从文件名提取域名: {domain}")
                        break

                print(f"[DEBUG] 最终域名: {domain}")
            except Exception as e:
                print(f"[DEBUG] 域名提取整体异常: {e}")
                domain = cert_folder

            # 清理域名，去除常见的后缀
            original_domain = domain
            suffixes_to_remove = ['_nginx', '_apache', '_iis', '_ssl', '_cert', '_bundle']
            for suffix in suffixes_to_remove:
                if domain.endswith(suffix):
                    domain = domain[:-len(suffix)]
                    print(f"[DEBUG] 域名清理: {original_domain} → {domain}")
                    break

            # 创建证书记录
            cursor.execute(
                'INSERT INTO certificates (domain, status, created_at, updated_at) VALUES (?, ?, ?, ?)',
                (domain, 'uploading', datetime.now(), datetime.now())
            )
            cert_id = cursor.lastrowid
            conn.commit()
            conn.close()

            # 异步执行自定义证书上传
            thread = threading.Thread(target=upload_ssl_certificate, args=(cert_id, 'custom', cert_folder, platforms))
            thread.daemon = True
            thread.start()

            flash(f'自定义证书上传已开始: {domain}', 'success')

        return redirect(url_for('certificates'))
    
    # 获取可上传的证书列表
    conn = sqlite3.connect(Config.DATABASE)
    cursor = conn.cursor()
    cursor.execute(
        'SELECT id, domain, status FROM certificates WHERE status = "completed" ORDER BY created_at DESC'
    )
    available_certs = cursor.fetchall()
    conn.close()
    
    return render_template('upload_certificate.html', certificates=available_certs)

@app.route('/api/certificate-folders')
def api_certificate_folders():
    """获取证书文件夹列表API"""
    try:
        cert_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'Certificates')
        cert_dir = os.path.abspath(cert_dir)

        if not os.path.exists(cert_dir):
            return jsonify({
                'success': False,
                'message': f'证书目录不存在: {cert_dir}',
                'folders': []
            })

        folders = []
        for item in os.listdir(cert_dir):
            item_path = os.path.join(cert_dir, item)
            if os.path.isdir(item_path):
                # 检查文件夹中的证书文件
                cert_files = []
                key_files = []

                for file in os.listdir(item_path):
                    if file.endswith('.key'):
                        key_files.append(file)
                    elif file.endswith(('.crt', '.pem')) and 'key' not in file.lower():
                        cert_files.append(file)

                if key_files and cert_files:
                    folders.append({
                        'name': item,
                        'cert_files': cert_files,
                        'key_files': key_files
                    })

        return jsonify({
            'success': True,
            'folders': folders,
            'cert_dir': cert_dir
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取证书文件夹失败: {str(e)}',
            'folders': []
        })

@app.route('/api/certificate-folder-info')
def api_certificate_folder_info():
    """获取证书文件夹详细信息API"""
    try:
        folder_name = request.args.get('folder')
        if not folder_name:
            return jsonify({
                'success': False,
                'message': '缺少文件夹名称参数'
            })

        cert_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'Certificates')
        folder_path = os.path.join(cert_dir, folder_name)

        if not os.path.exists(folder_path):
            return jsonify({
                'success': False,
                'message': f'证书文件夹不存在: {folder_name}'
            })

        # 查找证书文件
        key_file = None
        cert_file = None

        # 查找私钥文件
        for file in os.listdir(folder_path):
            if file.endswith('.key'):
                key_file = file
                break

        # 查找证书文件，优先查找bundle文件
        for pattern in ['*_bundle.crt', '*_integrated.crt', '*bundle*.crt', '*_bundle.pem', '*.crt', '*.pem']:
            import glob
            files = glob.glob(os.path.join(folder_path, pattern))
            for file_path in files:
                file_name = os.path.basename(file_path)
                if 'key' not in file_name.lower():
                    cert_file = file_name
                    break
            if cert_file:
                break

        if not key_file or not cert_file:
            return jsonify({
                'success': False,
                'message': '证书文件不完整，缺少私钥或证书文件'
            })

        # 尝试从证书文件中提取域名
        domain = folder_name  # 默认使用文件夹名
        cert_file_path = os.path.join(folder_path, cert_file)
        try:
            # 使用openssl提取域名
            result = subprocess.run(
                ['openssl', 'x509', '-in', cert_file_path, '-text', '-noout'],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                # 从Subject Alternative Name中提取域名
                for line in result.stdout.split('\n'):
                    if 'DNS:' in line:
                        dns_entries = line.split('DNS:')[1:]
                        if dns_entries:
                            extracted_domain = dns_entries[0].split(',')[0].strip()
                            if extracted_domain and not extracted_domain.startswith('*'):
                                domain = extracted_domain
                                break

                # 如果没有SAN，从Subject CN中提取
                if domain == folder_name:
                    for line in result.stdout.split('\n'):
                        if 'Subject:' in line and 'CN=' in line:
                            cn_part = line.split('CN=')[1].split(',')[0].strip()
                            if cn_part and not cn_part.startswith('*'):
                                domain = cn_part
                                break

                # 如果提取的域名是通配符，尝试从文件夹名中提取
                if domain.startswith('*.'):
                    # 尝试从文件夹名中提取域名
                    if '_' in folder_name:
                        potential_domain = folder_name.split('_')[0]
                        if '.' in potential_domain:
                            domain = potential_domain
        except:
            # 如果openssl失败，尝试从文件夹名中提取域名
            if '_' in folder_name:
                potential_domain = folder_name.split('_')[0]
                if '.' in potential_domain:
                    domain = potential_domain

        # 清理域名，去除常见的后缀
        original_domain = domain
        suffixes_to_remove = ['_nginx', '_apache', '_iis', '_ssl', '_cert', '_bundle']
        for suffix in suffixes_to_remove:
            if domain.endswith(suffix):
                domain = domain[:-len(suffix)]
                print(f"[DEBUG] 手动上传域名清理: {original_domain} → {domain}")
                break

        return jsonify({
            'success': True,
            'folder_name': folder_name,
            'cert_file': cert_file,
            'key_file': key_file,
            'domain': domain,
            'folder_path': folder_path
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取证书信息失败: {str(e)}'
        })

@app.route('/api/certificate-status')
def api_certificate_status():
    """获取证书状态API"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            'SELECT id, domain, status, updated_at, error_message, output_log FROM certificates ORDER BY created_at DESC'
        )
        certificates = cursor.fetchall()
        conn.close()

        cert_list = []
        for cert in certificates:
            cert_list.append({
                'id': cert[0],
                'domain': cert[1],
                'status': cert[2],
                'updated_at': cert[3],
                'error_message': cert[4],
                'output_log': cert[5]
            })

        return jsonify({
            'success': True,
            'certificates': cert_list
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取证书状态失败: {str(e)}'
        })

@app.route('/platform-updates')
def platform_updates():
    """云平台更新管理页面"""
    conn = sqlite3.connect(Config.DATABASE)
    cursor = conn.cursor()

    # 查询所有证书及其平台更新状态
    cursor.execute('''
        SELECT
            c.id as cert_id,
            c.domain,
            c.status as cert_status,
            c.created_at,
            p.id as update_id,
            p.platform_id,
            p.platform_name,
            p.status as platform_status,
            p.updated_at,
            p.notes
        FROM certificates c
        LEFT JOIN platform_updates p ON c.id = p.certificate_id
        ORDER BY c.created_at DESC, p.platform_name
    ''')

    results = cursor.fetchall()

    # 按证书分组整理数据
    certificates = {}
    for row in results:
        cert_id, domain, cert_status, created_at, update_id, platform_id, platform_name, platform_status, updated_at, notes = row

        if cert_id not in certificates:
            certificates[cert_id] = {
                'id': cert_id,
                'domain': domain,
                'status': cert_status,
                'created_at': created_at,
                'platforms': {}
            }

        # 如果有平台更新记录
        if update_id:
            certificates[cert_id]['platforms'][platform_id] = {
                'id': update_id,
                'name': platform_name,
                'status': platform_status or 'pending',
                'updated_at': updated_at,
                'notes': notes or ''
            }

    # 为没有任何平台记录的证书添加默认平台（只在首次时）
    for cert_id, cert_data in certificates.items():
        if not cert_data['platforms']:  # 只有当证书完全没有平台记录时才添加默认平台
            # 检查数据库中是否真的没有这个证书的任何平台记录
            cursor.execute('SELECT COUNT(*) FROM platform_updates WHERE certificate_id = ?', (cert_id,))
            count = cursor.fetchone()[0]

            if count == 0:  # 确实没有任何平台记录，添加默认平台
                for platform in Config.CLOUD_PLATFORMS:
                    # 直接插入到数据库中，而不是只在内存中添加
                    cursor.execute('''
                        INSERT INTO platform_updates (certificate_id, platform_id, platform_name, status, notes, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (cert_id, platform['id'], platform['name'], 'pending', '', datetime.now().isoformat()))

                    # 同时添加到内存中的数据结构
                    cert_data['platforms'][platform['id']] = {
                        'id': cursor.lastrowid,
                        'name': platform['name'],
                        'status': 'pending',
                        'updated_at': datetime.now().isoformat(),
                        'notes': ''
                    }

                conn.commit()

    conn.close()

    return render_template('platform_updates.html', certificates=certificates, platforms=Config.CLOUD_PLATFORMS)

@app.route('/api/update-platform-status', methods=['POST'])
def update_platform_status():
    """更新平台状态API"""
    try:
        data = request.get_json()
        update_id = data.get('update_id')
        cert_id = data.get('cert_id')
        platform_id = data.get('platform_id')
        status = data.get('status')
        notes = data.get('notes')

        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 如果已有更新记录，则更新
        if update_id and update_id != 'None':
            update_fields = []
            params = []

            if status is not None:
                update_fields.append('status = ?')
                params.append(status)

            if notes is not None:
                update_fields.append('notes = ?')
                params.append(notes)

            update_fields.append('updated_at = ?')
            params.append(datetime.now().isoformat())
            params.append(update_id)

            cursor.execute(f'''
                UPDATE platform_updates
                SET {', '.join(update_fields)}
                WHERE id = ?
            ''', params)
        else:
            # 创建新的更新记录
            platform_name = next((p['name'] for p in Config.CLOUD_PLATFORMS if p['id'] == platform_id), platform_id)

            cursor.execute('''
                INSERT INTO platform_updates (certificate_id, platform_id, platform_name, status, notes, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (cert_id, platform_id, platform_name, status or 'pending', notes or '', datetime.now().isoformat()))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '更新成功'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/certificate-history')
def certificate_history():
    """获取证书历史记录API"""
    try:
        domain = request.args.get('domain', '').strip()

        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 构建查询条件
        if domain:
            cursor.execute('''
                SELECT
                    c.domain,
                    p.platform_name,
                    p.status,
                    p.updated_at,
                    p.notes,
                    c.created_at as cert_created_at
                FROM platform_updates p
                JOIN certificates c ON p.certificate_id = c.id
                WHERE c.domain LIKE ?
                ORDER BY c.domain, p.updated_at DESC
            ''', (f'%{domain}%',))
        else:
            cursor.execute('''
                SELECT
                    c.domain,
                    p.platform_name,
                    p.status,
                    p.updated_at,
                    p.notes,
                    c.created_at as cert_created_at
                FROM platform_updates p
                JOIN certificates c ON p.certificate_id = c.id
                ORDER BY c.domain, p.updated_at DESC
                LIMIT 100
            ''')

        results = cursor.fetchall()

        # 格式化历史记录
        history = []
        for row in results:
            domain, platform_name, status, updated_at, notes, cert_created_at = row
            history.append({
                'domain': domain,
                'platform_name': platform_name,
                'status': status,
                'updated_at': updated_at,
                'notes': notes or '',
                'cert_created_at': cert_created_at
            })

        conn.close()

        return jsonify({'success': True, 'history': history})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/delete-platform', methods=['POST'])
def delete_platform():
    """删除平台API"""
    try:
        data = request.get_json()
        cert_id = data.get('cert_id')
        platform_id = data.get('platform_id')

        if not cert_id or not platform_id:
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 删除平台更新记录
        cursor.execute('''
            DELETE FROM platform_updates
            WHERE certificate_id = ? AND platform_id = ?
        ''', (cert_id, platform_id))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '删除成功'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/add-platform', methods=['POST'])
def add_platform():
    """添加平台API"""
    try:
        data = request.get_json()
        cert_id = data.get('cert_id')
        platform_id = data.get('platform_id')
        platform_name = data.get('platform_name')
        status = data.get('status', 'pending')
        notes = data.get('notes', '')

        if not cert_id or not platform_id or not platform_name:
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 检查是否已存在相同的平台记录
        cursor.execute('''
            SELECT id FROM platform_updates
            WHERE certificate_id = ? AND platform_id = ?
        ''', (cert_id, platform_id))

        existing = cursor.fetchone()
        if existing:
            conn.close()
            return jsonify({'success': False, 'message': '该平台已存在'}), 400

        # 插入新的平台记录
        cursor.execute('''
            INSERT INTO platform_updates (certificate_id, platform_id, platform_name, status, notes, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (cert_id, platform_id, platform_name, status, notes, datetime.now().isoformat()))

        update_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '添加成功', 'update_id': update_id})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/check-certificates')
def check_certificates():
    """证书检查页面"""
    conn = sqlite3.connect(Config.DATABASE)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, filename, platform, uploaded_at, domains_count
        FROM dns_uploads 
        ORDER BY uploaded_at DESC
    ''')
    
    dns_files = cursor.fetchall()
    
    # 不再默认显示检查结果，让用户主动点击检查
    check_results = []

    # 处理检查结果，计算剩余天数
    processed_results = []
    for result in check_results:
        domain, dns_record, status, expiry_date, checked_at, platform_detected = result

        # 计算剩余天数
        days_remaining_text = '-'
        if expiry_date:
            try:
                from datetime import datetime, date
                if ' ' in expiry_date:
                    expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d %H:%M:%S').date()
                else:
                    expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d').date()

                today = date.today()
                days_remaining = (expiry_dt - today).days

                if days_remaining > 0:
                    days_remaining_text = f"{days_remaining} 天"
                elif days_remaining == 0:
                    days_remaining_text = "今天过期"
                else:
                    days_remaining_text = f"已过期 {-days_remaining} 天"
            except:
                days_remaining_text = expiry_date

        processed_results.append((
            domain, dns_record, status, days_remaining_text, checked_at, platform_detected
        ))

    # 对结果进行排序：过期 > 警告 > 错误 > 正常
    def get_status_priority(status):
        priority_map = {
            'expired': 1,
            'warning': 2,
            'error': 3,
            'dns_error': 3,
            'ssl_error': 3,
            'timeout': 3,
            'valid': 4
        }
        return priority_map.get(status, 5)

    def extract_days_from_text(days_text):
        if not days_text or days_text == '-':
            return 999

        import re
        match = re.search(r'(\d+)', days_text)
        if match:
            days = int(match.group(1))
            if '已过期' in days_text:
                return -days
            return days

        if '今天过期' in days_text:
            return 0

        return 999

    # 排序逻辑
    processed_results.sort(key=lambda x: (
        get_status_priority(x[2]),  # 按状态优先级
        extract_days_from_text(x[3]) if x[2] in ['warning', 'expired'] else 0,  # 按剩余天数
        x[0]  # 按域名字母顺序
    ))

    conn.close()

    return render_template('check_certificates.html', dns_files=dns_files, check_results=processed_results)

@app.route('/api/dns-files/<int:file_id>', methods=['DELETE'])
def delete_dns_file(file_id):
    """删除DNS文件"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取文件信息
        cursor.execute('SELECT filename, file_path FROM dns_uploads WHERE id = ?', (file_id,))
        file_info = cursor.fetchone()

        if not file_info:
            return jsonify({'success': False, 'message': '文件不存在'})

        filename, file_path = file_info

        # 删除数据库记录
        cursor.execute('DELETE FROM dns_uploads WHERE id = ?', (file_id,))

        # 注意：暂时不删除相关的域名检查记录，因为表结构中没有dns_file_id字段
        # 如果需要删除相关记录，可以考虑添加dns_file_id字段或通过其他方式关联

        conn.commit()
        conn.close()

        # 删除物理文件
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            logging.warning(f"删除文件失败: {e}")

        return jsonify({
            'success': True,
            'message': f'文件 {filename} 删除成功'
        })

    except Exception as e:
        logging.error(f"删除DNS文件失败: {e}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@app.route('/api/dns-files/<int:file_id>/domains')
def get_dns_file_domains(file_id):
    """获取DNS文件中的域名列表"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取文件信息
        cursor.execute('SELECT filename, platform, file_path FROM dns_uploads WHERE id = ?', (file_id,))
        file_info = cursor.fetchone()

        if not file_info:
            return jsonify({'success': False, 'message': '文件不存在'})

        filename, platform, file_path = file_info

        # 重新解析文件获取域名列表
        if file_path and os.path.exists(file_path):
            domains, stats = parse_dns_file(file_path, platform)

            return jsonify({
                'success': True,
                'data': {
                    'file_id': file_id,
                    'filename': filename,
                    'platform': platform,
                    'domains': domains,
                    'stats': stats
                }
            })
        else:
            return jsonify({'success': False, 'message': '文件不存在'})

    except Exception as e:
        logging.error(f"获取DNS文件域名失败: {e}")
        return jsonify({'success': False, 'message': f'获取失败: {str(e)}'})

# 后台任务函数
def apply_ssl_certificate(cert_id, domain, cert_type='rsa'):
    """申请SSL证书的后台任务"""
    try:
        # 根据证书类型选择脚本
        if cert_type == 'rsa':
            # 使用RSA专用脚本
            script_path = '../update-ssl/auto_rsa_ssl.sh'
            logging.info(f"申请RSA证书: {domain}")
        else:
            # 使用默认ECC脚本
            script_path = Config.AUTO_SSL_SCRIPT
            logging.info(f"申请ECC证书: {domain}")

        # 调用对应的脚本
        result = subprocess.run(
            ['bash', script_path, domain],
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        
        if result.returncode == 0:
            cursor.execute(
                'UPDATE certificates SET status = ?, updated_at = ? WHERE id = ?',
                ('completed', datetime.now(), cert_id)
            )
            
            # 创建云平台更新记录
            for platform in Config.CLOUD_PLATFORMS:
                cursor.execute(
                    'INSERT INTO platform_updates (certificate_id, platform_id, platform_name) VALUES (?, ?, ?)',
                    (cert_id, platform['id'], platform['name'])
                )
        else:
            cursor.execute(
                'UPDATE certificates SET status = ?, error_message = ?, updated_at = ? WHERE id = ?',
                ('failed', result.stderr, datetime.now(), cert_id)
            )
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE certificates SET status = ?, error_message = ?, updated_at = ? WHERE id = ?',
            ('failed', str(e), datetime.now(), cert_id)
        )
        conn.commit()
        conn.close()

def upload_ssl_certificate(cert_id, upload_type='auto', cert_folder=None, platforms=None):
    """上传SSL证书的后台任务"""
    try:
        if upload_type == 'custom' and cert_folder:
            # 上传自定义证书
            # 切换到update-ssl目录执行脚本
            base_dir = os.path.dirname(os.path.abspath(__file__))
            script_dir = os.path.join(base_dir, '..', 'update-ssl')
            script_dir = os.path.abspath(script_dir)

            # 设置环境变量控制平台上传
            env = os.environ.copy()
            if platforms:
                env['UPLOAD_TO_HUAWEI'] = 'true' if platforms.get('huawei', False) else 'false'
                env['UPLOAD_TO_TENCENT'] = 'true' if platforms.get('tencent', False) else 'false'
                env['UPLOAD_TO_VOLCANO'] = 'true' if platforms.get('volcengine', False) else 'false'
                env['UPLOAD_TO_ALIYUN'] = 'true' if platforms.get('aliyun', False) else 'false'

            # 构建完整的证书文件夹路径（使用正斜杠，兼容bash）
            cert_folder_path = f'../Certificates/{cert_folder}'

            # 添加调试信息
            print(f"[DEBUG] 自定义证书上传参数:")
            print(f"[DEBUG]   cert_folder: {cert_folder}")
            print(f"[DEBUG]   cert_folder_path: {cert_folder_path}")
            print(f"[DEBUG]   platforms: {platforms}")
            print(f"[DEBUG]   环境变量:")
            for key in ['UPLOAD_TO_HUAWEI', 'UPLOAD_TO_TENCENT', 'UPLOAD_TO_VOLCANO', 'UPLOAD_TO_ALIYUN']:
                print(f"[DEBUG]     {key}: {env.get(key, 'not set')}")

            # 在Windows上，需要特殊处理环境变量传递给bash
            env_vars = []
            if platforms:
                if platforms.get('huawei', False):
                    env_vars.append('UPLOAD_TO_HUAWEI=true')
                if platforms.get('tencent', False):
                    env_vars.append('UPLOAD_TO_TENCENT=true')
                if platforms.get('volcengine', False):
                    env_vars.append('UPLOAD_TO_VOLCANO=true')
                if platforms.get('aliyun', False):
                    env_vars.append('UPLOAD_TO_ALIYUN=true')

            # 构建bash命令，包含环境变量
            env_string = ' '.join(env_vars) if env_vars else ''
            bash_command = f"{env_string} ./upload_custom_ssl.sh '{cert_folder_path}'" if env_string else f"./upload_custom_ssl.sh '{cert_folder_path}'"

            result = subprocess.run(
                ['bash', '-c', bash_command],
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时
                encoding='utf-8',
                errors='ignore',
                cwd=script_dir  # 在update-ssl目录中执行
            )

            # 添加输出调试信息
            print(f"[DEBUG] 脚本执行结果:")
            print(f"[DEBUG]   返回码: {result.returncode}")
            print(f"[DEBUG]   标准输出: {result.stdout}")
            print(f"[DEBUG]   标准错误: {result.stderr}")
        else:
            # 自动申请证书上传
            # 设置环境变量控制平台上传
            env = os.environ.copy()
            if platforms:
                env['UPLOAD_TO_HUAWEI'] = 'true' if platforms.get('huawei', False) else 'false'
                env['UPLOAD_TO_TENCENT'] = 'true' if platforms.get('tencent', False) else 'false'
                env['UPLOAD_TO_VOLCANO'] = 'true' if platforms.get('volcengine', False) else 'false'
                env['UPLOAD_TO_ALIYUN'] = 'true' if platforms.get('aliyun', False) else 'false'

            # 调用new_upload_ssl.sh脚本
            result = subprocess.run(
                ['bash', Config.UPLOAD_SSL_SCRIPT],
                capture_output=True,
                text=True,
                timeout=600,  # 10分钟超时
                encoding='utf-8',
                errors='ignore',
                env=env  # 传递环境变量
            )

        # 记录详细的输出信息
        output_info = f"返回码: {result.returncode}\n"
        if result.stdout:
            output_info += f"标准输出:\n{result.stdout}\n"
        if result.stderr:
            output_info += f"错误输出:\n{result.stderr}\n"

        if upload_type == 'custom':
            # 自定义证书上传，更新现有记录
            conn = sqlite3.connect(Config.DATABASE)
            cursor = conn.cursor()

            if result.returncode == 0:
                success_indicators = ['上传成功', '证书上传成功', 'SUCCESS']
                has_success = any(indicator in result.stdout for indicator in success_indicators)

                status = 'uploaded' if has_success else 'partial_success'
                cursor.execute(
                    'UPDATE certificates SET status = ?, output_log = ?, updated_at = ? WHERE id = ?',
                    (status, output_info, datetime.now(), cert_id)
                )
            else:
                cursor.execute(
                    'UPDATE certificates SET status = ?, error_message = ?, output_log = ?, updated_at = ? WHERE id = ?',
                    ('upload_failed', result.stderr or '上传脚本执行失败', output_info, datetime.now(), cert_id)
                )

            conn.commit()
            conn.close()
        else:
            # 自动申请证书上传，更新现有记录
            conn = sqlite3.connect(Config.DATABASE)
            cursor = conn.cursor()

            if result.returncode == 0:
                # 检查输出中是否包含成功信息
                success_indicators = ['上传成功', '证书上传成功', 'SUCCESS']
                has_success = any(indicator in result.stdout for indicator in success_indicators)

                if has_success:
                    cursor.execute(
                        'UPDATE certificates SET status = ?, output_log = ?, updated_at = ? WHERE id = ?',
                        ('uploaded', output_info, datetime.now(), cert_id)
                    )
                else:
                    # 虽然返回码为0，但可能部分平台失败
                    cursor.execute(
                        'UPDATE certificates SET status = ?, output_log = ?, updated_at = ? WHERE id = ?',
                        ('partial_success', output_info, datetime.now(), cert_id)
                    )
            else:
                cursor.execute(
                    'UPDATE certificates SET status = ?, error_message = ?, output_log = ?, updated_at = ? WHERE id = ?',
                    ('upload_failed', result.stderr or '上传脚本执行失败', output_info, datetime.now(), cert_id)
                )

            conn.commit()
            conn.close()
        
    except Exception as e:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            'UPDATE certificates SET status = ?, error_message = ?, updated_at = ? WHERE id = ?',
            ('upload_failed', str(e), datetime.now(), cert_id)
        )
        conn.commit()
        conn.close()

# API接口

@app.route('/api/platform-updates/update', methods=['POST'])
def api_update_platform_status():
    """更新平台状态API"""
    data = request.get_json()

    if not data or 'update_id' not in data or 'status' not in data:
        return jsonify({'success': False, 'message': '参数错误'}), 400

    conn = sqlite3.connect(Config.DATABASE)
    cursor = conn.cursor()

    cursor.execute(
        'UPDATE platform_updates SET status = ?, notes = ?, updated_at = ? WHERE id = ?',
        (data['status'], data.get('notes', ''), datetime.now(), data['update_id'])
    )

    conn.commit()
    conn.close()

    return jsonify({'success': True, 'message': '状态更新成功'})

@app.route('/upload-dns', methods=['POST'])
def upload_dns():
    """DNS文件上传处理 - 支持AJAX和表单提交"""
    # 检查是否是AJAX请求
    is_ajax = (request.headers.get('X-Requested-With') == 'XMLHttpRequest' or
               request.headers.get('Accept', '').find('application/json') != -1 or
               request.args.get('ajax') == '1')

    if 'dns_file' not in request.files:
        if is_ajax:
            return jsonify({'success': False, 'message': '没有上传文件'}), 400
        flash('没有上传文件', 'error')
        return redirect(url_for('check_certificates'))

    file = request.files['dns_file']
    platform = request.form.get('platform')
    auto_check = request.form.get('auto_check') == 'on'

    if file.filename == '':
        if is_ajax:
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        flash('没有选择文件', 'error')
        return redirect(url_for('check_certificates'))

    if not platform:
        if is_ajax:
            return jsonify({'success': False, 'message': '请选择云平台'}), 400
        flash('请选择云平台', 'error')
        return redirect(url_for('check_certificates'))

    try:
        # 保存文件
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file_path = os.path.join(Config.UPLOAD_FOLDER, filename)
        file.save(file_path)

        # 解析DNS文件获取域名列表
        print(f"开始解析文件: {file_path}, 平台: {platform}")
        domains, parse_stats = parse_dns_file(file_path, platform)
        domains_count = len(domains)
        print(f"解析完成: 域名数量={domains_count}, 统计={parse_stats}")

        # 记录解析统计信息
        if parse_stats.get('errors'):
            logging.warning(f"DNS文件解析警告: {parse_stats['errors']}")

        logging.info(f"DNS文件解析完成: 总记录数={parse_stats.get('total_records', 0)}, "
                    f"有效域名={parse_stats.get('valid_domains', 0)}, "
                    f"跳过记录={parse_stats.get('skipped_records', 0)}")

        # 保存到数据库
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO dns_uploads (filename, platform, domains_count, file_path) VALUES (?, ?, ?, ?)',
            (file.filename, platform, domains_count, file_path)
        )
        upload_id = cursor.lastrowid
        conn.commit()
        conn.close()

        # 如果选择自动检查，启动检查任务
        if auto_check and domains_count > 0:
            thread = threading.Thread(target=check_certificates_task, args=(upload_id, domains))
            thread.daemon = True
            thread.start()

        # 根据请求类型返回不同响应
        if is_ajax:
            return jsonify({
                'success': True,
                'message': f'DNS文件上传成功，解析出 {domains_count} 个域名',
                'data': {
                    'upload_id': upload_id,
                    'filename': file.filename,
                    'platform': platform,
                    'domains_count': domains_count,
                    'parse_stats': parse_stats,
                    'auto_check': auto_check
                }
            })
        else:
            flash(f'DNS文件上传成功，解析出 {domains_count} 个域名', 'success')
            if auto_check and domains_count > 0:
                flash('证书检查已开始，请稍后查看结果', 'info')
            return redirect(url_for('check_certificates'))

    except Exception as e:
        error_msg = f'文件上传失败: {str(e)}'
        logging.error(error_msg)

        if is_ajax:
            return jsonify({'success': False, 'message': error_msg}), 500
        else:
            flash(error_msg, 'error')
            return redirect(url_for('check_certificates'))

@app.route('/api/dns/upload', methods=['POST'])
def api_upload_dns():
    """DNS文件上传API"""
    if 'dns_file' not in request.files:
        return jsonify({'success': False, 'message': '没有上传文件'}), 400

    file = request.files['dns_file']
    platform = request.form.get('platform')

    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'}), 400

    if not platform:
        return jsonify({'success': False, 'message': '请选择云平台'}), 400

    try:
        # 保存文件
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file_path = os.path.join(Config.UPLOAD_FOLDER, filename)
        file.save(file_path)

        # 解析DNS文件获取域名列表
        domains, parse_stats = parse_dns_file(file_path, platform)
        domains_count = len(domains)

        # 记录解析统计信息
        if parse_stats.get('errors'):
            logging.warning(f"DNS文件解析警告: {parse_stats['errors']}")

        logging.info(f"DNS文件解析完成: 总记录数={parse_stats.get('total_records', 0)}, "
                    f"有效域名={parse_stats.get('valid_domains', 0)}, "
                    f"跳过记录={parse_stats.get('skipped_records', 0)}")

        # 保存到数据库
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO dns_uploads (filename, platform, domains_count, file_path) VALUES (?, ?, ?, ?)',
            (file.filename, platform, domains_count, file_path)
        )
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'data': {
                'filename': file.filename,
                'platform': platform,
                'domains_count': domains_count
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'}), 500

@app.route('/api/certificates/check', methods=['POST'])
def api_check_certificates():
    """证书检查API"""
    data = request.get_json()

    if not data or 'dns_file_id' not in data:
        return jsonify({'success': False, 'message': '参数错误'}), 400

    # 异步执行证书检查
    thread = threading.Thread(target=check_certificates_by_file_id, args=(data['dns_file_id'],))
    thread.daemon = True
    thread.start()

    return jsonify({'success': True, 'message': '证书检查已开始'})

def check_certificates_by_file_id(dns_file_id):
    """根据文件ID检查证书的后台任务"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取DNS文件信息
        cursor.execute('SELECT file_path, platform FROM dns_uploads WHERE id = ?', (dns_file_id,))
        file_info = cursor.fetchone()

        if not file_info:
            return

        file_path, platform = file_info

        # 这里应该调用ssl_checker工具进行检查
        # 简化实现：模拟检查结果
        import csv

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)  # 跳过标题行

                for row in reader:
                    if len(row) >= 2:
                        domain = row[0].strip()
                        dns_record = row[1].strip() if len(row) > 1 else ''

                        # 模拟SSL检查结果
                        ssl_status = 'valid'  # 实际应该调用ssl_checker
                        expiry_date = '2024-12-31'  # 实际应该从证书中获取

                        cursor.execute('''
                            INSERT OR REPLACE INTO domain_checks
                            (domain, dns_record, ssl_status, expiry_date, platform_detected)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (domain, dns_record, ssl_status, expiry_date, platform))

        except Exception as e:
            print(f"检查证书时出错: {e}")

        conn.commit()
        conn.close()

    except Exception as e:
        print(f"证书检查任务出错: {e}")

@app.route('/api/check-results')
def api_check_results():
    """获取证书检查结果API"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取最近的检查结果
        cursor.execute('''
        SELECT domain, ssl_status as status, expiry_date, checked_at, platform_detected
        FROM domain_checks
        ORDER BY checked_at DESC
        LIMIT 100
        ''')

        results = []
        for row in cursor.fetchall():
            domain, ssl_status, expiry_date, checked_at, platform_detected = row

            # 计算剩余天数
            days_remaining = 0
            if expiry_date:
                try:
                    from datetime import datetime
                    expiry = datetime.strptime(expiry_date, '%Y-%m-%d')
                    days_remaining = (expiry - datetime.now()).days
                except:
                    pass

            results.append({
                'domain': domain,
                'ssl_status': ssl_status,
                'expiry_date': expiry_date,
                'days_remaining': days_remaining,
                'checked_at': checked_at,
                'platform_detected': platform_detected
            })

        conn.close()
        return jsonify({'success': True, 'data': results})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/manual-check', methods=['POST'])
def api_manual_check():
    """手动检查单个域名的SSL证书"""
    try:
        data = request.get_json()
        domain = data.get('domain', '').strip()
        batch_id = data.get('batch_id')  # 获取批次ID
        dns_file_id = data.get('dns_file_id')  # 获取DNS文件ID

        if not domain:
            return jsonify({'success': False, 'message': '请输入域名'})

        # 检查SSL证书
        result = check_ssl_certificate(domain)

        # 从指定的DNS文件中查找DNS记录值
        dns_record = find_dns_record_from_file(domain, dns_file_id)

        # 保存结果到数据库
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute('''
        INSERT INTO domain_checks
        (domain, dns_record, ssl_status, expiry_date, platform_detected, batch_id, dns_file_id)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            domain,
            dns_record,
            result.get('status', 'error'),
            result.get('valid_to', ''),
            'manual_check',
            batch_id,
            dns_file_id
        ))

        # 更新检查任务进度
        cursor.execute('''
            UPDATE check_tasks
            SET completed_domains = completed_domains + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE batch_id = ?
        ''', (batch_id,))

        # 检查是否所有域名都已完成
        cursor.execute('''
            SELECT total_domains, completed_domains
            FROM check_tasks
            WHERE batch_id = ?
        ''', (batch_id,))

        task_info = cursor.fetchone()
        if task_info and task_info[0] == task_info[1]:  # total_domains == completed_domains
            cursor.execute('''
                UPDATE check_tasks
                SET status = 'completed',
                    updated_at = CURRENT_TIMESTAMP
                WHERE batch_id = ?
            ''', (batch_id,))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'data': result})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/check-task-status/<batch_id>')
def get_check_task_status(batch_id):
    """获取检查任务状态"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取任务状态
        cursor.execute('''
            SELECT total_domains, completed_domains, status, dns_file_id
            FROM check_tasks
            WHERE batch_id = ?
        ''', (batch_id,))

        task = cursor.fetchone()

        if not task:
            return jsonify({'success': False, 'message': '任务不存在'})

        total_domains, completed_domains, status, dns_file_id = task

        # 获取各状态的统计
        cursor.execute('''
            SELECT ssl_status, COUNT(*)
            FROM domain_checks
            WHERE batch_id = ?
            GROUP BY ssl_status
        ''', (batch_id,))

        status_counts = dict(cursor.fetchall())

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'batch_id': batch_id,
                'dns_file_id': dns_file_id,
                'total_domains': total_domains,
                'completed_domains': completed_domains,
                'status': status,
                'progress_percentage': (completed_domains / total_domains * 100) if total_domains > 0 else 0,
                'status_counts': {
                    'valid': status_counts.get('valid', 0),
                    'warning': status_counts.get('warning', 0),
                    'expired': status_counts.get('expired', 0),
                    'error': status_counts.get('error', 0)
                }
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/active-check-tasks')
def get_active_check_tasks():
    """获取当前活跃的检查任务"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取正在运行的任务
        cursor.execute('''
            SELECT ct.batch_id, ct.dns_file_id, ct.total_domains, ct.completed_domains,
                   du.filename, du.platform
            FROM check_tasks ct
            JOIN dns_uploads du ON ct.dns_file_id = du.id
            WHERE ct.status = 'running'
            ORDER BY ct.created_at DESC
        ''')

        tasks = cursor.fetchall()
        conn.close()

        active_tasks = []
        for task in tasks:
            batch_id, dns_file_id, total_domains, completed_domains, filename, platform = task
            active_tasks.append({
                'batch_id': batch_id,
                'dns_file_id': dns_file_id,
                'filename': filename,
                'platform': platform,
                'total_domains': total_domains,
                'completed_domains': completed_domains,
                'progress_percentage': (completed_domains / total_domains * 100) if total_domains > 0 else 0
            })

        return jsonify({'success': True, 'data': active_tasks})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/dns-file-results/<int:file_id>')
def api_dns_file_results(file_id):
    """获取特定DNS文件的检查结果"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取该DNS文件最新批次的检查结果
        cursor.execute('''
            SELECT batch_id, MAX(checked_at) as latest_check
            FROM domain_checks
            WHERE dns_file_id = ? AND batch_id IS NOT NULL
            GROUP BY batch_id
            ORDER BY latest_check DESC
            LIMIT 1
        ''', (file_id,))

        latest_batch = cursor.fetchone()

        if latest_batch:
            # 获取最新批次的所有检查结果
            cursor.execute('''
                SELECT domain, dns_record, ssl_status, expiry_date, checked_at, platform_detected
                FROM domain_checks
                WHERE dns_file_id = ? AND batch_id = ?
                ORDER BY
                    CASE ssl_status
                        WHEN 'expired' THEN 1
                        WHEN 'warning' THEN 2
                        WHEN 'expiring' THEN 3
                        WHEN 'valid' THEN 4
                        ELSE 5
                    END,
                    checked_at DESC
            ''', (file_id, latest_batch[0]))

            check_results = cursor.fetchall()
        else:
            check_results = []

        # 处理检查结果，计算剩余天数
        processed_results = []
        for result in check_results:
            domain, dns_record, ssl_status, expiry_date, checked_at, platform_detected = result

            # 计算剩余天数
            days_remaining_text = '-'
            if expiry_date:
                try:
                    from datetime import datetime, date
                    if ' ' in expiry_date:
                        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d %H:%M:%S').date()
                    else:
                        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d').date()

                    today = date.today()
                    days_remaining = (expiry_dt - today).days

                    if days_remaining > 0:
                        days_remaining_text = f"{days_remaining} 天"
                    elif days_remaining == 0:
                        days_remaining_text = "今天过期"
                    else:
                        days_remaining_text = f"已过期 {-days_remaining} 天"
                except:
                    days_remaining_text = expiry_date

            processed_results.append((
                domain,
                dns_record,
                ssl_status,
                days_remaining_text,
                checked_at,
                platform_detected
            ))

        conn.close()
        return jsonify({'success': True, 'data': processed_results})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/add-certificate', methods=['POST'])
def api_add_certificate():
    """添加付费证书API"""
    try:
        data = request.get_json()
        domain = data.get('domain', '').strip()
        cert_type = data.get('type', 'paid')
        provider = data.get('provider', '').strip()
        notes = data.get('notes', '').strip()

        if not domain:
            return jsonify({'success': False, 'message': '请输入证书域名'})

        # 简单的域名格式验证
        import re
        domain_regex = r'^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$'
        if not re.match(domain_regex, domain):
            return jsonify({'success': False, 'message': '请输入有效的域名格式'})

        # 检查域名是否已存在
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        cursor.execute('SELECT id FROM certificates WHERE domain = ?', (domain,))
        existing = cursor.fetchone()

        if existing:
            conn.close()
            return jsonify({'success': False, 'message': '该域名的证书已存在'})

        # 插入证书记录
        cursor.execute('''
            INSERT INTO certificates (domain, status, created_at, updated_at, error_message)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            domain,
            'manual_added',  # 手动添加的状态
            datetime.now(),
            datetime.now(),
            f'付费证书 - 类型:{cert_type}, 提供商:{provider}, 备注:{notes}'
        ))

        cert_id = cursor.lastrowid

        # 为该证书创建各云平台的更新任务
        platforms = Config.CLOUD_PLATFORMS
        for platform in platforms:
            cursor.execute('''
                INSERT INTO platform_updates (certificate_id, platform_id, platform_name, status, updated_at, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                cert_id,
                platform['id'],
                platform['name'],
                'pending',  # 待更新状态
                datetime.now(),
                f'付费证书自动创建的更新任务 - {cert_type}'
            ))

        conn.commit()
        conn.close()

        logging.info(f"添加付费证书成功: 域名={domain}, 类型={cert_type}, 提供商={provider}")

        return jsonify({
            'success': True,
            'message': '证书添加成功',
            'data': {
                'cert_id': cert_id,
                'domain': domain,
                'type': cert_type,
                'provider': provider,
                'platform_tasks_created': len(platforms)
            }
        })

    except Exception as e:
        logging.error(f"添加付费证书失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})

@app.route('/api/ssl-stats')
def api_ssl_stats():
    """获取SSL检查统计信息API"""
    try:
        from ssl_checker_integration import SSLCheckerIntegration

        checker = SSLCheckerIntegration(Config.DATABASE)
        stats = checker.get_check_statistics()

        return jsonify({
            'success': True,
            'data': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/batch-check', methods=['POST'])
def api_batch_check():
    """批量检查域名SSL证书API"""
    try:
        data = request.get_json()
        domains = data.get('domains', [])

        if not domains:
            return jsonify({'success': False, 'message': '域名列表不能为空'})

        if len(domains) > 100:
            return jsonify({'success': False, 'message': '一次最多只能检查100个域名'})

        # 启动后台检查任务
        thread = threading.Thread(
            target=check_certificates_task,
            args=(None, domains)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': f'已开始检查 {len(domains)} 个域名',
            'data': {
                'domain_count': len(domains),
                'estimated_time': len(domains) * 2  # 估计时间（秒）
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/latest-check-results')
def api_latest_check_results():
    """获取最新的检查结果API"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 获取最新的批次ID
        cursor.execute('''
            SELECT batch_id
            FROM domain_checks
            WHERE batch_id IS NOT NULL
            ORDER BY checked_at DESC
            LIMIT 1
        ''')

        latest_batch_result = cursor.fetchone()
        latest_batch_id = latest_batch_result[0] if latest_batch_result else None

        if latest_batch_id:
            # 获取最新批次的所有结果
            cursor.execute('''
                SELECT domain, dns_record, ssl_status, expiry_date, checked_at, platform_detected
                FROM domain_checks
                WHERE batch_id = ?
                ORDER BY checked_at DESC
            ''', (latest_batch_id,))
        else:
            # 如果没有批次ID，返回最新的50条记录
            cursor.execute('''
                SELECT domain, dns_record, ssl_status, expiry_date, checked_at, platform_detected
                FROM domain_checks
                ORDER BY checked_at DESC
                LIMIT 50
            ''')

        check_results = cursor.fetchall()

        # 处理检查结果，计算剩余天数
        processed_results = []
        for result in check_results:
            domain, dns_record, status, expiry_date, checked_at, platform_detected = result

            # 计算剩余天数
            days_remaining_text = '-'
            if expiry_date:
                try:
                    from datetime import datetime, date
                    if ' ' in expiry_date:
                        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d %H:%M:%S').date()
                    else:
                        expiry_dt = datetime.strptime(expiry_date, '%Y-%m-%d').date()

                    today = date.today()
                    days_remaining = (expiry_dt - today).days

                    if days_remaining > 0:
                        days_remaining_text = f"{days_remaining} 天"
                    elif days_remaining == 0:
                        days_remaining_text = "今天过期"
                    else:
                        days_remaining_text = f"已过期 {-days_remaining} 天"
                except:
                    days_remaining_text = expiry_date

            processed_results.append({
                'domain': domain,
                'dns_record': dns_record,
                'ssl_status': status,
                'days_remaining': days_remaining_text,
                'checked_at': checked_at,
                'platform_detected': platform_detected
            })

        # 对API结果也进行排序
        def get_status_priority(status):
            priority_map = {
                'expired': 1,
                'warning': 2,
                'error': 3,
                'dns_error': 3,
                'ssl_error': 3,
                'timeout': 3,
                'valid': 4
            }
            return priority_map.get(status, 5)

        def extract_days_from_text(days_text):
            if not days_text or days_text == '-':
                return 999

            import re
            match = re.search(r'(\d+)', days_text)
            if match:
                days = int(match.group(1))
                if '已过期' in days_text:
                    return -days
                return days

            if '今天过期' in days_text:
                return 0

            return 999

        # 排序
        processed_results.sort(key=lambda x: (
            get_status_priority(x['ssl_status']),
            extract_days_from_text(x['days_remaining']) if x['ssl_status'] in ['warning', 'expired'] else 0,
            x['domain']
        ))

        conn.close()

        return jsonify({
            'success': True,
            'data': processed_results
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/create-check-task', methods=['POST'])
def create_check_task():
    """创建检查任务"""
    try:
        data = request.get_json()
        batch_id = data.get('batch_id')
        dns_file_id = data.get('dns_file_id')
        total_domains = data.get('total_domains')

        if not all([batch_id, dns_file_id, total_domains]):
            return jsonify({'success': False, 'message': '参数不完整'})

        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 创建检查任务
        cursor.execute('''
            INSERT OR REPLACE INTO check_tasks
            (batch_id, dns_file_id, total_domains, completed_domains, status)
            VALUES (?, ?, ?, 0, 'running')
        ''', (batch_id, dns_file_id, total_domains))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': '检查任务已创建'})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

# 腾讯云证书相关API
@app.route('/api/tencent-certificates')
def api_tencent_certificates():
    """获取腾讯云证书列表API（从缓存读取）"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 从缓存中读取证书数据
        cursor.execute('''
            SELECT cert_id, domain, cert_begin_time, cert_end_time, status, status_name,
                   alias, subject_alt_name, tags, is_expiring, insert_time,
                   certificate_type, days_remaining, cached_at
            FROM tencent_certificates_cache
            ORDER BY days_remaining ASC, cert_end_time ASC
        ''')

        cached_certs = cursor.fetchall()
        conn.close()

        if not cached_certs:
            # 如果缓存为空，返回提示信息
            return jsonify({
                'success': True,
                'message': '证书缓存为空，请点击刷新按钮获取最新数据',
                'certificates': [],
                'from_cache': False,
                'cache_empty': True
            })

        # 处理缓存的证书数据
        processed_certs = []
        for cert_row in cached_certs:
            (cert_id, domain, cert_begin_time, cert_end_time, status, status_name,
             alias, subject_alt_name, tags, is_expiring, insert_time,
             certificate_type, days_remaining, cached_at) = cert_row

            try:
                # 重新计算剩余天数（基于当前时间）
                end_time = datetime.strptime(cert_end_time, '%Y-%m-%d %H:%M:%S')
                now = datetime.now()
                current_days_remaining = (end_time - now).days

                # 判断状态
                if current_days_remaining < 0:
                    status_class = 'expired'
                    status_text = '已过期'
                elif current_days_remaining <= 30:
                    status_class = 'expiring'
                    status_text = '即将过期'
                else:
                    status_class = 'valid'
                    status_text = '正常'

                # 解析标签
                try:
                    import json
                    tags_list = json.loads(tags) if tags else []
                except:
                    tags_list = []

                processed_cert = {
                    'cert_id': cert_id,
                    'domain': domain,
                    'alias': alias or domain,
                    'cert_begin_time': cert_begin_time,
                    'cert_end_time': cert_end_time,
                    'days_remaining': current_days_remaining,
                    'status': status,
                    'status_name': status_name or '',
                    'status_class': status_class,
                    'status_text': status_text,
                    'is_expiring': bool(is_expiring),
                    'insert_time': insert_time or '',
                    'certificate_type': certificate_type or 'SVR',
                    'tags': tags_list,
                    'cached_at': cached_at
                }
                processed_certs.append(processed_cert)

            except Exception as e:
                logging.error(f"处理缓存证书 {cert_id} 时出错: {e}")
                continue

        # 获取缓存时间信息
        cache_time = None
        if cached_certs:
            cache_time = cached_certs[0][-1]  # cached_at字段

        return jsonify({
            'success': True,
            'message': f'从缓存获取 {len(processed_certs)} 个证书',
            'certificates': processed_certs,
            'from_cache': True,
            'cache_time': cache_time,
            'cache_empty': False
        })

    except Exception as e:
        logging.error(f"从缓存获取腾讯云证书列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取证书列表失败: {str(e)}',
            'certificates': [],
            'from_cache': False
        })


@app.route('/api/refresh-tencent-certificates', methods=['POST'])
def refresh_tencent_certificates():
    """刷新腾讯云证书缓存"""
    try:
        # 导入腾讯云更新器
        try:
            from update_tencent import TencentCertUpdater
        except ImportError as e:
            logging.error(f"导入腾讯云更新器失败: {e}")
            return jsonify({
                'success': False,
                'message': '腾讯云SDK未安装或配置错误'
            })

        # 从环境变量获取腾讯云密钥
        access_key = os.environ.get('TENCENT_ACCESS_KEY')
        secret_key = os.environ.get('TENCENT_SECRET_KEY')

        if not access_key or not secret_key:
            return jsonify({
                'success': False,
                'message': '腾讯云密钥未配置，请设置环境变量 TENCENT_ACCESS_KEY 和 TENCENT_SECRET_KEY'
            })

        # 创建腾讯云更新器实例
        updater = TencentCertUpdater(access_key, secret_key, region='ap-beijing')

        # 获取标签为 company=baoqu 的证书
        certificates = updater.get_certificates_by_tag('company', 'baoqu')

        if not certificates:
            return jsonify({
                'success': False,
                'message': '未找到标签为 company=baoqu 的证书'
            })

        # 更新缓存
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        # 清空现有缓存
        cursor.execute('DELETE FROM tencent_certificates_cache')

        # 插入新的证书数据
        updated_count = 0
        for cert in certificates:
            try:
                # 计算剩余天数
                end_time = datetime.strptime(cert['cert_end_time'], '%Y-%m-%d %H:%M:%S')
                now = datetime.now()
                days_remaining = (end_time - now).days

                # 处理标签数据
                import json
                tags_json = json.dumps(cert.get('tags', []), ensure_ascii=False)

                # 处理SAN域名
                subject_alt_name = json.dumps(cert.get('subject_alt_name', []), ensure_ascii=False)

                cursor.execute('''
                    INSERT INTO tencent_certificates_cache
                    (cert_id, domain, cert_begin_time, cert_end_time, status, status_name,
                     alias, subject_alt_name, tags, is_expiring, insert_time,
                     certificate_type, days_remaining, cached_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    cert['cert_id'],
                    cert['domain'],
                    cert['cert_begin_time'],
                    cert['cert_end_time'],
                    cert['status'],
                    cert.get('status_name', ''),
                    cert.get('alias', ''),
                    subject_alt_name,
                    tags_json,
                    cert.get('is_expiring', False),
                    cert.get('insert_time', ''),
                    cert.get('certificate_type', 'SVR'),
                    days_remaining,
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                updated_count += 1

            except Exception as e:
                logging.error(f"缓存证书 {cert.get('cert_id', 'unknown')} 时出错: {e}")
                continue

        conn.commit()
        conn.close()

        logging.info(f"腾讯云证书缓存刷新成功，更新了 {updated_count} 个证书")

        return jsonify({
            'success': True,
            'message': f'证书缓存刷新成功，更新了 {updated_count} 个证书',
            'updated_count': updated_count,
            'refresh_time': datetime.now().isoformat()
        })

    except Exception as e:
        logging.error(f"刷新腾讯云证书缓存失败: {e}")
        return jsonify({
            'success': False,
            'message': f'刷新证书缓存失败: {str(e)}'
        })

# 证书更新相关API
@app.route('/api/update-certificate', methods=['POST'])
def update_certificate():
    """执行证书更新"""
    try:
        data = request.get_json()
        cert_id = data.get('cert_id')
        platform_id = data.get('platform_id')
        update_id = data.get('update_id')

        if not all([cert_id, platform_id, update_id]):
            return jsonify({'success': False, 'message': '参数不完整'}), 400

        # 获取证书信息
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        cursor.execute('SELECT domain, status FROM certificates WHERE id = ?', (cert_id,))
        cert_result = cursor.fetchone()

        if not cert_result:
            conn.close()
            return jsonify({'success': False, 'message': '证书不存在'}), 404

        domain, cert_status = cert_result

        # 更新平台状态为进行中
        cursor.execute('''
            UPDATE platform_updates
            SET status = 'in_progress', updated_at = ?, notes = '开始更新...'
            WHERE id = ?
        ''', (datetime.now().isoformat(), update_id))

        conn.commit()
        conn.close()

        # 启动后台更新任务
        thread = threading.Thread(
            target=execute_certificate_update,
            args=(cert_id, platform_id, update_id, domain)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'message': '更新任务已启动',
            'status': 'in_progress'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': f'启动更新失败: {str(e)}'}), 500

def execute_certificate_update(cert_id, platform_id, update_id, domain):
    """执行证书更新的后台任务"""
    try:
        # 更新状态为进行中
        update_platform_notes(update_id, '正在执行证书更新...')

        # 对于腾讯云，直接从腾讯云API查找证书ID
        if platform_id == 'tencent':
            update_platform_notes(update_id, '🔍 正在腾讯云中查找域名对应的证书...')

            # 创建腾讯云配置
            tencent_config = {
                'access_key': os.environ.get('TENCENT_ACCESS_KEY', ''),
                'secret_key': os.environ.get('TENCENT_SECRET_KEY', ''),
                'region': 'ap-beijing'
            }

            # 通过腾讯云API查找证书ID
            cert_ids = find_tencent_cert_ids(domain, tencent_config)
            if not cert_ids:
                update_platform_notes(update_id, f'❌ 未在腾讯云中找到域名 {domain} 的证书')
                update_platform_status(update_id, 'failed')
                return

            update_platform_notes(update_id, f'✅ 找到证书: 新证书ID={cert_ids["new_cert_id"]}, 旧证书ID={cert_ids["old_cert_id"]}')
            cert_info = cert_ids.copy()  # 直接使用证书ID信息
            cert_info['domain'] = domain  # 添加域名信息
        else:
            # 其他云平台仍需要查找本地证书文件
            update_platform_notes(update_id, '🔍 正在查找证书文件...')
            cert_info = get_certificate_files(domain, cert_id)

            if not cert_info:
                update_platform_notes(update_id, f'❌ 未找到域名 {domain} 的证书文件')
                update_platform_status(update_id, 'failed')
                return

        # 显示证书来源信息
        source_info = {
            'letsencrypt': '🆓 Let\'s Encrypt免费证书',
            'local_certificates': '💰 付费证书（本地目录）',
            'database_path': '📁 数据库记录路径',
            'latest_found': '🔍 智能搜索找到'
        }

        cert_type_info = {
            'free': '（免费证书）',
            'paid': '（付费证书）',
            'unknown': ''
        }

        source_text = source_info.get(cert_info['source'], cert_info['source'])
        type_text = cert_type_info.get(cert_info.get('cert_type', ''), '')

        update_platform_notes(update_id, f'''✅ 找到证书文件:
  来源: {source_text}{type_text}
  证书: {cert_info["cert_file"]}
  私钥: {cert_info["key_file"]}''')

        # 第二步：构建更新脚本路径
        ssl_update_dir = os.path.join(os.path.dirname(__file__), '..', 'ssl-update')
        ssl_update_dir = os.path.abspath(ssl_update_dir)

        # 根据平台选择对应的更新脚本
        script_map = {
            'tencent': 'update_tencent.py',
            'aliyun': 'update_aliyun.py',
            'huawei': 'update_huawei.py',
            'kingsoft': 'update_kingsoft.py'
        }

        script_name = script_map.get(platform_id)
        if not script_name:
            update_platform_notes(update_id, f'❌ 不支持的云平台: {platform_id}')
            update_platform_status(update_id, 'failed')
            return

        script_path = os.path.join(ssl_update_dir, script_name)

        if not os.path.exists(script_path):
            update_platform_notes(update_id, f'❌ 更新脚本不存在: {script_path}')
            update_platform_status(update_id, 'failed')
            return

        # 第三步：准备配置文件（腾讯云不需要配置文件）
        config_file = None
        if platform_id != 'tencent':
            update_platform_notes(update_id, '⚙️ 正在准备配置文件...')
            config_file = create_update_config(platform_id, domain, cert_info)

            if not config_file:
                update_platform_notes(update_id, f'❌ 无法创建 {platform_id} 的配置文件')
                update_platform_status(update_id, 'failed')
                return

        # 第四步：执行更新脚本
        update_platform_notes(update_id, f'🚀 正在执行 {get_platform_name(platform_id)} 证书更新...')

        # 根据平台选择不同的执行方式
        if platform_id == 'tencent':
            # 腾讯云使用直接参数调用
            success, output = execute_tencent_update_script(script_path, cert_info, ssl_update_dir)
        else:
            # 其他平台使用配置文件调用
            success, output = execute_update_script(script_path, config_file, ssl_update_dir)

        # 清理临时配置文件
        if config_file:
            try:
                os.remove(config_file)
            except:
                pass

        # 第五步：处理结果
        if success:
            update_platform_notes(update_id, output)
            update_platform_status(update_id, 'completed')
        else:
            update_platform_notes(update_id, output)
            update_platform_status(update_id, 'failed')


    except Exception as e:
        error_text = f"❌ 更新过程中发生异常: {str(e)}\n异常时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        update_platform_notes(update_id, error_text)
        update_platform_status(update_id, 'failed')

def update_platform_status(update_id, status):
    """更新平台状态"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE platform_updates
            SET status = ?, updated_at = ?
            WHERE id = ?
        ''', (status, datetime.now().isoformat(), update_id))
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"更新状态失败: {e}")

def update_platform_notes(update_id, notes):
    """更新平台备注"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE platform_updates
            SET notes = ?, updated_at = ?
            WHERE id = ?
        ''', (notes, datetime.now().isoformat(), update_id))
        conn.commit()
        conn.close()
    except Exception as e:
        print(f"更新备注失败: {e}")

def get_platform_name(platform_id):
    """获取平台名称"""
    platform_names = {
        'tencent': '腾讯云',
        'aliyun': '阿里云',
        'huawei': '华为云',
        'kingsoft': '金山云',
        'volcano': '火山引擎',
        'baishan': '白山云'
    }
    return platform_names.get(platform_id, platform_id)

def get_certificate_files(domain, cert_id):
    """获取证书文件路径"""
    try:
        # 方法1：从Let's Encrypt目录查找（免费证书）
        letsencrypt_paths = [
            f"\\\\wsl.localhost\\Ubuntu\\etc\\letsencrypt\\live\\{domain}",
            f"/etc/letsencrypt/live/{domain}",  # Linux路径
            f"C:\\etc\\letsencrypt\\live\\{domain}"  # 备选路径
        ]

        for letsencrypt_path in letsencrypt_paths:
            if os.path.exists(letsencrypt_path):
                cert_file = os.path.join(letsencrypt_path, "fullchain.pem")
                key_file = os.path.join(letsencrypt_path, "privkey.pem")

                if os.path.exists(cert_file) and os.path.exists(key_file):
                    return {
                        'cert_file': cert_file,
                        'key_file': key_file,
                        'source': 'letsencrypt',
                        'cert_type': 'free'
                    }

        # 方法2：从本地Certificates目录查找（付费证书）
        base_dir = os.path.dirname(os.path.abspath(__file__))
        cert_dir = os.path.join(base_dir, '..', 'Certificates')
        cert_dir = os.path.abspath(cert_dir)

        # 尝试多种可能的文件夹命名格式，包括通配符证书
        possible_folders = [
            f"{domain}_nginx",
            f"{domain}",
            domain.replace('.', '_'),
            domain.replace('.', '-')
        ]

        # 如果是通配符域名，添加star格式的文件夹名
        if domain.startswith('*.'):
            star_domain = domain.replace('*.', 'star.')
            possible_folders.extend([
                f"{star_domain}_nginx",
                f"{star_domain}",
                star_domain.replace('.', '_'),
                star_domain.replace('.', '-')
            ])

        for folder_name in possible_folders:
            folder_path = os.path.join(cert_dir, folder_name)
            if os.path.exists(folder_path):
                # 查找证书文件
                cert_files = []
                key_files = []

                for file in os.listdir(folder_path):
                    file_path = os.path.join(folder_path, file)
                    if file.endswith(('.crt', '.pem')) and 'bundle' in file:
                        cert_files.append(file_path)
                    elif file.endswith('.key'):
                        key_files.append(file_path)

                if cert_files and key_files:
                    return {
                        'cert_file': cert_files[0],
                        'key_file': key_files[0],
                        'source': 'local_certificates',
                        'cert_type': 'paid'
                    }

        # 方法3：从数据库查找证书路径
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()
        cursor.execute('SELECT cert_path FROM certificates WHERE id = ?', (cert_id,))
        result = cursor.fetchone()
        conn.close()

        if result and result[0]:
            cert_path = result[0]
            if os.path.exists(cert_path):
                # 假设cert_path是证书文件，尝试找到对应的私钥文件
                cert_file = cert_path
                key_file = cert_path.replace('.crt', '.key').replace('.pem', '.key')

                if os.path.exists(key_file):
                    return {
                        'cert_file': cert_file,
                        'key_file': key_file,
                        'source': 'database_path',
                        'cert_type': 'unknown'
                    }

        # 方法4：查找最新的证书文件（按修改时间）
        if os.path.exists(cert_dir):
            all_cert_files = []
            for root, dirs, files in os.walk(cert_dir):
                for file in files:
                    if domain in file and file.endswith(('.crt', '.pem')):
                        file_path = os.path.join(root, file)
                        all_cert_files.append((file_path, os.path.getmtime(file_path)))

            if all_cert_files:
                # 按修改时间排序，取最新的
                all_cert_files.sort(key=lambda x: x[1], reverse=True)
                latest_cert = all_cert_files[0][0]

                # 尝试找到对应的私钥
                cert_dir_path = os.path.dirname(latest_cert)
                for file in os.listdir(cert_dir_path):
                    if file.endswith('.key') and domain in file:
                        key_file = os.path.join(cert_dir_path, file)
                        return {
                            'cert_file': latest_cert,
                            'key_file': key_file,
                            'source': 'latest_found',
                            'cert_type': 'paid'
                        }

        return None

    except Exception as e:
        print(f"获取证书文件失败: {e}")
        return None

def extract_cert_id_from_upload_output(upload_output):
    """从上传输出中提取证书ID"""
    try:
        import json
        import re

        # 查找JSON格式的证书ID输出
        # 腾讯云返回格式: {"CertificateId": "Pt8pZROH", "RepeatCertId": "", "RequestId": "..."}
        json_pattern = r'\{\s*"CertificateId"\s*:\s*"([^"]+)"'
        match = re.search(json_pattern, upload_output)

        if match:
            cert_id = match.group(1)
            print(f"从上传输出中提取到证书ID: {cert_id}")
            return cert_id

        # 如果没找到JSON格式，尝试其他格式
        # 可能的格式: CertificateId: Pt8pZROH
        simple_pattern = r'CertificateId[:\s]+([A-Za-z0-9]+)'
        match = re.search(simple_pattern, upload_output)

        if match:
            cert_id = match.group(1)
            print(f"从上传输出中提取到证书ID: {cert_id}")
            return cert_id

        print("未能从上传输出中提取证书ID")
        return None

    except Exception as e:
        print(f"提取证书ID失败: {e}")
        return None

def find_tencent_cert_ids(domain, tencent_config):
    """动态查找腾讯云中对应域名的证书ID"""
    try:
        access_key = tencent_config.get('access_key')
        secret_key = tencent_config.get('secret_key')

        if not access_key or not secret_key:
            print("腾讯云访问密钥未配置")
            return None

        # 导入腾讯云更新模块
        import sys
        ssl_update_path = os.path.join(os.path.dirname(__file__), '..', 'ssl-update')
        if ssl_update_path not in sys.path:
            sys.path.append(ssl_update_path)

        from update_tencent import TencentCertUpdater

        # 创建腾讯云更新器实例
        updater = TencentCertUpdater(access_key, secret_key)

        # 查找该域名的所有证书
        matching_certs = updater.find_certificates_by_domain(domain)

        if not matching_certs:
            print(f"未找到域名 {domain} 的证书")
            return None

        # 如果只有一个证书，新旧证书ID相同（续期场景）
        if len(matching_certs) == 1:
            cert_id = matching_certs[0]['cert_id']
            print(f"找到域名 {domain} 的证书ID: {cert_id}")
            return {
                "new_cert_id": cert_id,
                "old_cert_id": cert_id
            }

        # 如果有多个证书，最新的作为new_cert_id，第二新的作为old_cert_id
        elif len(matching_certs) >= 2:
            new_cert_id = matching_certs[0]['cert_id']  # 最新的
            old_cert_id = matching_certs[1]['cert_id']  # 第二新的
            print(f"找到域名 {domain} 的证书: 新={new_cert_id}, 旧={old_cert_id}")
            return {
                "new_cert_id": new_cert_id,
                "old_cert_id": old_cert_id
            }

        return None

    except Exception as e:
        print(f"查找腾讯云证书ID失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def create_update_config(platform_id, domain, cert_info):
    """创建更新配置文件"""
    try:
        import tempfile

        # 创建临时配置文件
        config_data = {
            "cert_source": {
                "type": "files",
                "new_cert_file": cert_info['cert_file'],
                "new_key_file": cert_info['key_file'],
                "old_cert_file": cert_info['cert_file'],  # 暂时使用相同文件
                "old_key_file": cert_info['key_file'],    # 实际应该是旧证书
                "domain": domain
            },
            "providers": {}
        }

        # 根据平台添加配置
        if platform_id == 'tencent':
            # 腾讯云需要证书ID，尝试从腾讯云控制台获取
            tencent_config = {
                "access_key": os.environ.get('TENCENT_ACCESS_KEY', ''),
                "secret_key": os.environ.get('TENCENT_SECRET_KEY', ''),
                "region": "ap-beijing",
                "resource_types": ["clb"],
                "regions": ["ap-guangzhou", "ap-beijing"]
            }

            # 如果已经从上传中获取了新证书ID，优先使用
            if 'new_cert_id' in cert_info:
                tencent_config['new_cert_id'] = cert_info['new_cert_id']

                # 查找旧证书ID（用于替换）
                cert_ids = find_tencent_cert_ids(domain, tencent_config)
                if cert_ids and 'old_cert_id' in cert_ids:
                    tencent_config['old_cert_id'] = cert_ids['old_cert_id']
                else:
                    # 如果找不到旧证书，使用新证书ID（续期场景）
                    tencent_config['old_cert_id'] = cert_info['new_cert_id']
            else:
                # 尝试通过API查找腾讯云中对应域名的证书ID
                cert_ids = find_tencent_cert_ids(domain, tencent_config)
                if cert_ids:
                    tencent_config.update(cert_ids)
                else:
                    # 如果找不到，使用占位符并记录错误
                    tencent_config.update({
                        "new_cert_id": "CERT_ID_NOT_FOUND",
                        "old_cert_id": "CERT_ID_NOT_FOUND"
                    })
                    print(f"警告: 未找到域名 {domain} 在腾讯云中的证书ID")

            config_data["providers"]["tencent"] = tencent_config
        elif platform_id == 'aliyun':
            config_data["providers"]["aliyun"] = {
                "access_key": os.environ.get('ALIYUN_ACCESS_KEY', ''),
                "secret_key": os.environ.get('ALIYUN_SECRET_KEY', ''),
                "lb_list": []  # 需要从数据库或配置中获取
            }
        elif platform_id == 'huawei':
            config_data["providers"]["huawei"] = {
                "access_key": os.environ.get('HUAWEI_ACCESS_KEY', ''),
                "secret_key": os.environ.get('HUAWEI_SECRET_KEY', ''),
                "region": "cn-north-4"
            }
        elif platform_id == 'kingsoft':
            config_data["providers"]["kingsoft"] = {
                "access_key": os.environ.get('KINGSOFT_ACCESS_KEY', ''),
                "secret_key": os.environ.get('KINGSOFT_SECRET_KEY', ''),
                "lb_list": []  # 需要从数据库或配置中获取
            }

        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
            return f.name

    except Exception as e:
        print(f"创建配置文件失败: {e}")
        return None

def execute_tencent_update_script(script_path, cert_info, work_dir):
    """执行腾讯云更新脚本"""
    try:
        # 构建腾讯云更新脚本的参数
        cmd = [
            sys.executable,
            os.path.basename(script_path),
            '--access-key', os.environ.get('TENCENT_ACCESS_KEY', ''),
            '--secret-key', os.environ.get('TENCENT_SECRET_KEY', ''),
            '--region', 'ap-beijing'
        ]

        # 根据证书信息选择调用方式
        if 'new_cert_id' in cert_info and 'old_cert_id' in cert_info:
            # 使用证书ID模式
            cmd.extend(['--cert-ids', cert_info['new_cert_id'], cert_info['old_cert_id']])
        else:
            # 使用域名模式（从cert_info中提取域名）
            domain = cert_info.get('domain', '')
            if domain:
                cmd.extend(['--domain', domain])
            else:
                return False, "❌ 缺少域名或证书ID信息"

        result = subprocess.run(
            cmd,
            cwd=work_dir,
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            encoding='utf-8',
            errors='ignore'
        )

        # 处理输出
        output = ""
        if result.stdout:
            output += result.stdout
        if result.stderr:
            output += f"\n错误信息:\n{result.stderr}"

        success = result.returncode == 0

        if not output.strip():
            if success:
                output = "✅ 腾讯云证书更新成功"
            else:
                output = f"❌ 腾讯云证书更新失败，返回码: {result.returncode}"

        return success, output

    except subprocess.TimeoutExpired:
        return False, "❌ 腾讯云更新脚本执行超时（5分钟）"
    except Exception as e:
        return False, f"❌ 腾讯云更新脚本执行异常: {str(e)}"

def execute_update_script(script_path, config_file, work_dir):
    """执行更新脚本"""
    try:
        # 执行Python更新脚本
        cmd = [sys.executable, os.path.basename(script_path), '--config', config_file]

        result = subprocess.run(
            cmd,
            cwd=work_dir,
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            encoding='utf-8',
            errors='ignore'
        )

        # 处理输出
        output = ""
        if result.stdout:
            output += result.stdout
        if result.stderr:
            output += f"\n错误信息:\n{result.stderr}"

        success = result.returncode == 0

        if not output.strip():
            if success:
                output = "✅ 更新脚本执行成功，但没有输出信息"
            else:
                output = f"❌ 更新脚本执行失败，返回码: {result.returncode}"

        return success, output

    except subprocess.TimeoutExpired:
        return False, "❌ 更新脚本执行超时（5分钟）"
    except Exception as e:
        return False, f"❌ 执行更新脚本时发生异常: {str(e)}"

@app.route('/api/get-update-status/<int:update_id>')
def get_update_status(update_id):
    """获取更新状态"""
    try:
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT status, notes, updated_at
            FROM platform_updates
            WHERE id = ?
        ''', (update_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            status, notes, updated_at = result
            return jsonify({
                'success': True,
                'status': status,
                'notes': notes or '',
                'updated_at': updated_at
            })
        else:
            return jsonify({'success': False, 'message': '记录不存在'}), 404

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500


@app.route('/api/download-tencent-dns', methods=['POST'])
def download_tencent_dns():
    """下载腾讯云DNS记录"""
    try:
        data = request.get_json()
        domain = data.get('domain', '').strip()

        if not domain:
            return jsonify({'success': False, 'message': '域名不能为空'})

        # 导入腾讯云DNS客户端
        import sys
        import os
        ssl_checker_path = os.path.join(os.path.dirname(__file__), '..', 'ssl_checker')
        sys.path.append(ssl_checker_path)

        from tencent_dns_downloader import TencentDNSClient

        # 从配置或环境变量获取API密钥
        secret_id = "AKIDSiiZ8QRBgurYx5vVptgCSR9algLjqgFX"  # 应该从配置文件读取
        secret_key = "********************************"    # 应该从配置文件读取

        # 创建DNS客户端
        dns_client = TencentDNSClient(secret_id, secret_key)

        # 获取DNS记录
        records = dns_client.get_domain_records(domain)

        if not records:
            return jsonify({'success': False, 'message': f'未找到域名 {domain} 的DNS记录'})

        # 设置输出目录
        output_dir = os.path.join(os.path.dirname(__file__), '..', 'DNS', '腾迅云dns记录')

        # 导出到Excel
        file_path = dns_client.export_records_to_excel(domain, records, output_dir)

        if not file_path:
            return jsonify({'success': False, 'message': '导出DNS记录失败'})

        # 解析文件获取域名列表
        domains, parse_stats = parse_dns_file(file_path, 'tencent')
        domains_count = len(domains)

        # 保存到数据库
        conn = sqlite3.connect(Config.DATABASE)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO dns_uploads (filename, platform, domains_count, file_path)
            VALUES (?, ?, ?, ?)
        ''', (os.path.basename(file_path), 'tencent', domains_count, file_path))

        conn.commit()
        conn.close()

        logging.info(f"腾讯云DNS记录下载成功: {domain}, 记录数: {len(records)}, 域名数: {domains_count}")

        return jsonify({
            'success': True,
            'message': f'域名 {domain} 的DNS记录下载成功',
            'domain': domain,
            'records_count': len(records),
            'domains_count': domains_count,
            'file_path': file_path
        })

    except Exception as e:
        logging.error(f"下载腾讯云DNS记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'})


@app.route('/api/tencent-api-status')
def tencent_api_status():
    """检查腾讯云API连接状态"""
    try:
        # 导入腾讯云DNS客户端
        import sys
        import os
        ssl_checker_path = os.path.join(os.path.dirname(__file__), '..', 'ssl_checker')
        sys.path.append(ssl_checker_path)

        from tencent_dns_downloader import TencentDNSClient

        # 从配置或环境变量获取API密钥
        secret_id = "AKIDSiiZ8QRBgurYx5vVptgCSR9algLjqgFX"  # 应该从配置文件读取
        secret_key = "********************************"    # 应该从配置文件读取

        # 创建DNS客户端并测试连接
        dns_client = TencentDNSClient(secret_id, secret_key)
        domains = dns_client.list_domains()

        return jsonify({
            'success': True,
            'message': '腾讯云API连接正常',
            'domains_count': len(domains)
        })

    except Exception as e:
        logging.error(f"检查腾讯云API状态失败: {str(e)}")
        return jsonify({'success': False, 'message': f'连接失败: {str(e)}'})


if __name__ == '__main__':
    # 打印调试信息
    print(f"🔍 数据库路径调试信息:")
    print(f"   Config.DATABASE: {Config.DATABASE}")
    print(f"   绝对路径: {os.path.abspath(Config.DATABASE)}")
    print(f"   文件存在: {os.path.exists(Config.DATABASE)}")
    if os.path.exists(Config.DATABASE):
        print(f"   文件大小: {os.path.getsize(Config.DATABASE):,} 字节")

    # 初始化数据库
    init_db()

    # 启动应用
    app.logger.info("SSL管理平台启动")
    app.run(debug=True, host='0.0.0.0', port=5000)
