#!/bin/bash

echo "=== Certbot安装脚本 ==="
echo "自动检测并安装certbot"
echo

# 检查是否已经安装
if command -v certbot >/dev/null 2>&1; then
    echo "✅ certbot已经安装"
    certbot --version
    exit 0
fi

echo "🔍 certbot未安装，开始安装..."

# 更新包列表
echo "📦 更新包列表..."
sudo apt update

# 方法1: 尝试使用apt安装
echo "🚀 方法1: 使用apt安装certbot..."
if sudo apt install -y certbot; then
    echo "✅ 使用apt安装成功"
    certbot --version
    exit 0
else
    echo "❌ apt安装失败，尝试其他方法..."
fi

# 方法2: 尝试使用snap安装
echo "🚀 方法2: 使用snap安装certbot..."
if command -v snap >/dev/null 2>&1; then
    echo "snap已安装，直接安装certbot..."
else
    echo "安装snap..."
    sudo apt install -y snapd
    
    # 启动snap服务
    sudo systemctl enable snapd
    sudo systemctl start snapd
    
    # 等待snap初始化
    sleep 5
fi

if sudo snap install --classic certbot; then
    echo "✅ 使用snap安装成功"
    
    # 创建符号链接
    sudo ln -sf /snap/bin/certbot /usr/bin/certbot
    
    certbot --version
    exit 0
else
    echo "❌ snap安装也失败了..."
fi

# 方法3: 尝试使用pipx安装
echo "🚀 方法3: 使用pipx安装certbot..."
if ! command -v pipx >/dev/null 2>&1; then
    echo "安装pipx..."
    sudo apt install -y python3-pip python3-venv
    sudo apt install -y pipx || pip3 install --user pipx
fi

if pipx install certbot; then
    echo "✅ 使用pipx安装成功"
    
    # 确保路径正确
    pipx ensurepath
    
    # 添加到PATH
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
    source ~/.bashrc
    
    certbot --version
    exit 0
else
    echo "❌ pipx安装也失败了..."
fi

# 方法4: 手动下载安装
echo "🚀 方法4: 手动下载certbot..."
cd /tmp

# 下载certbot-auto（已废弃，但可能仍然可用）
if wget https://dl.eff.org/certbot-auto; then
    chmod +x certbot-auto
    sudo mv certbot-auto /usr/local/bin/certbot
    
    echo "✅ 手动安装成功"
    certbot --version
    exit 0
else
    echo "❌ 手动下载也失败了..."
fi

echo "❌ 所有安装方法都失败了"
echo "请手动检查网络连接和系统配置"
echo
echo "💡 手动安装建议:"
echo "1. 检查网络连接: ping google.com"
echo "2. 检查系统版本: lsb_release -a"
echo "3. 检查Python版本: python3 --version"
echo "4. 尝试强制pip安装: pip3 install --break-system-packages certbot"
echo "5. 或者使用Docker: docker run -it certbot/certbot --version"

exit 1
