#!/bin/bash

# 测试RSA脚本的基本功能
echo "=== 测试RSA证书申请脚本 ==="

# 检查脚本是否存在
if [ ! -f "auto_rsa_ssl.sh" ]; then
    echo "❌ RSA脚本不存在: auto_rsa_ssl.sh"
    exit 1
fi

echo "✅ RSA脚本文件存在"

# 检查脚本权限
if [ ! -x "auto_rsa_ssl.sh" ]; then
    echo "⚠️  RSA脚本没有执行权限，尝试添加权限..."
    chmod +x auto_rsa_ssl.sh
    if [ $? -eq 0 ]; then
        echo "✅ 成功添加执行权限"
    else
        echo "❌ 无法添加执行权限"
        exit 1
    fi
else
    echo "✅ RSA脚本有执行权限"
fi

# 测试脚本语法
echo "检查脚本语法..."
bash -n auto_rsa_ssl.sh
if [ $? -eq 0 ]; then
    echo "✅ 脚本语法正确"
else
    echo "❌ 脚本语法错误"
    exit 1
fi

# 测试无参数调用（应该显示错误信息）
echo "测试无参数调用..."
bash auto_rsa_ssl.sh 2>&1 | head -5
echo "✅ 无参数测试完成"

echo "=== RSA脚本基本测试完成 ==="
