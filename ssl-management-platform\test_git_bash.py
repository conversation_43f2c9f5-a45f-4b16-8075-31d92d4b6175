#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Git Bash执行
"""

import subprocess
import os

def test_git_bash():
    """测试Git Bash执行"""
    print("=== 测试Git Bash执行 ===")
    
    git_bash_path = r"C:\Program Files\Git\bin\bash.exe"
    script_path = "../update-ssl/auto_ssl.sh"
    test_domain = "test.example.com"
    
    # 检查Git Bash是否存在
    if not os.path.exists(git_bash_path):
        print(f"❌ Git Bash不存在: {git_bash_path}")
        return False
    
    print(f"✅ Git Bash存在: {git_bash_path}")
    
    # 检查脚本是否存在
    if not os.path.exists(script_path):
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    print(f"✅ 脚本存在: {script_path}")
    
    # 测试不同的执行方式
    methods = [
        {
            'name': 'Git Bash 直接',
            'command': [git_bash_path, script_path, test_domain]
        },
        {
            'name': 'Git Bash 绝对路径',
            'command': [git_bash_path, os.path.abspath(script_path), test_domain]
        },
        {
            'name': 'Git Bash 无参数',
            'command': [git_bash_path, script_path]
        }
    ]
    
    for method in methods:
        print(f"\n测试: {method['name']}")
        print(f"命令: {' '.join(method['command'])}")
        
        try:
            result = subprocess.run(
                method['command'],
                capture_output=True,
                text=True,
                timeout=10,  # 短超时
                encoding='utf-8',
                errors='ignore'
            )
            
            print(f"   返回码: {result.returncode}")
            
            output = (result.stdout + result.stderr)[:300]
            print(f"   输出: {output.replace(chr(10), ' ')}")
            
            # 检查是否有预期的输出
            if '错误' in output or '用法' in output or 'certbot' in output:
                print(f"   ✅ {method['name']}: 脚本响应正常")
                return True
            
        except subprocess.TimeoutExpired:
            print("   ⏰ 超时（脚本可能开始执行了）")
            return True
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    return False

def test_certificate_apply_with_git_bash():
    """使用Git Bash测试证书申请"""
    print("\n=== 使用Git Bash测试证书申请 ===")
    
    # 导入后端函数
    import sys
    sys.path.append('.')
    
    try:
        from app import apply_ssl_certificate
        import sqlite3
        
        # 创建测试记录
        conn = sqlite3.connect('ssl_management.db')
        cursor = conn.cursor()
        
        test_domain = "test-gitbash.example.com"
        cursor.execute(
            'INSERT INTO certificates (domain, status, error_message) VALUES (?, ?, ?)',
            (test_domain, 'applying', '测试Git Bash RSA证书申请')
        )
        cert_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"创建测试记录: ID={cert_id}, 域名={test_domain}")
        
        # 调用申请函数
        import threading
        import time
        
        def run_apply():
            try:
                apply_ssl_certificate(cert_id, test_domain, 'rsa')
                print("✅ Git Bash申请函数执行完成")
            except Exception as e:
                print(f"❌ Git Bash申请函数执行失败: {e}")
        
        thread = threading.Thread(target=run_apply)
        thread.daemon = True
        thread.start()
        
        # 等待一段时间
        print("等待Git Bash申请函数执行...")
        thread.join(timeout=15)
        
        if thread.is_alive():
            print("⏰ Git Bash申请函数仍在执行中")
        
        # 检查结果
        time.sleep(2)
        
        conn = sqlite3.connect('ssl_management.db')
        cursor = conn.cursor()
        cursor.execute(
            'SELECT status, error_message FROM certificates WHERE id = ?',
            (cert_id,)
        )
        result = cursor.fetchone()
        conn.close()
        
        if result:
            status, error_msg = result
            print(f"   状态: {status}")
            print(f"   错误信息: {error_msg or '无'}")
            
            if 'bash: not found' not in (error_msg or ''):
                print("✅ Git Bash执行成功（没有bash not found错误）")
                return True
            else:
                print("❌ 仍然有bash not found错误")
                return False
        else:
            print("❌ 未找到测试记录")
            return False
            
    except Exception as e:
        print(f"❌ Git Bash申请测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始测试Git Bash解决方案...\n")
    
    # 1. 测试Git Bash基本功能
    git_bash_ok = test_git_bash()
    
    # 2. 测试证书申请
    if git_bash_ok:
        apply_ok = test_certificate_apply_with_git_bash()
    else:
        apply_ok = False
    
    print("\n" + "="*50)
    print("测试结果:")
    print("="*50)
    
    print(f"Git Bash基本功能: {'✅ 通过' if git_bash_ok else '❌ 失败'}")
    print(f"证书申请功能: {'✅ 通过' if apply_ok else '❌ 失败'}")
    
    if git_bash_ok and apply_ok:
        print("\n🎉 Git Bash解决方案成功！")
        print("现在可以尝试申请kimg.cn的RSA证书了")
    else:
        print("\n⚠️  Git Bash解决方案需要进一步调试")
        
    print("\n💡 下一步:")
    print("1. 重启SSL管理平台")
    print("2. 尝试申请kimg.cn的RSA证书")
    print("3. 检查是否还有bash not found错误")

if __name__ == "__main__":
    main()
