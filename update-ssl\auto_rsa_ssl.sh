#!/bin/bash

# 你的域名
DOMAIN="$1"
##首次部署 - 强制生成RSA证书
SERVER="--server https://acme.freessl.cn/v2/DV90/directory/ynn8fo1fe1j9uut8istn"

depoly_ssl()
{
certbot certonly --manual --key-type rsa --rsa-key-size 4096 -d *.$DOMAIN -d $DOMAIN $SERVER
}


##更新证书 - 强制生成RSA证书
update_ssl()
{
# 使用expect来自动输入值
expect << EOF
spawn certbot certonly --force-renewal --key-type rsa --rsa-key-size 4096 -d *.$DOMAIN  -d $DOMAIN $SERVER
expect "1-2"
send "2\r"
expect eof
EOF
}


# 检查域名参数
if [ -z "$DOMAIN" ]; then
    echo "❌ 错误: 请提供域名参数"
    echo "用法: $0 <域名>"
    echo "示例: $0 example.com"
    exit 1
fi

echo "🚀 开始为域名 $DOMAIN 申请RSA SSL证书..."

# 只删除当前域名的证书目录（如果存在）
if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
    echo "删除现有证书: $DOMAIN"
    rm -rf "/etc/letsencrypt/live/$DOMAIN"
fi

# 申请证书
echo "正在申请RSA证书（4096位）..."
depoly_ssl

# 检查证书申请是否成功（查找所有可能的证书目录）
echo "检查证书申请结果..."
CERT_FOUND=false

# 查找包含域名的证书目录（不要求完全匹配）
for cert_dir in /etc/letsencrypt/live/*${DOMAIN}*; do
    if [ -d "$cert_dir" ] && [ -f "$cert_dir/fullchain.pem" ]; then
        echo "✅ 找到证书目录: $cert_dir"
        CERT_FOUND=true
        ACTUAL_CERT_DIR="$cert_dir"
        break
    fi
done

if [ "$CERT_FOUND" = true ]; then
    echo "✅ RSA证书申请成功！"
    echo "证书目录: $ACTUAL_CERT_DIR"
    chmod -R 755 /etc/letsencrypt/live/

    # 等待2秒确保文件完全写入
    echo "等待证书文件完全生成..."
    sleep 2
else
    echo "❌ RSA证书申请失败！未找到证书文件"
    exit 1
fi

# 同步证书到项目Certificates目录
sync_to_project() {
    echo "开始同步RSA证书到项目目录..."

    # 检查是否有包含域名的证书目录
    SYNC_CERT_FOUND=false
    for cert_dir in /etc/letsencrypt/live/*${DOMAIN}*; do
        if [ -d "$cert_dir" ] && [ -f "$cert_dir/fullchain.pem" ]; then
            echo "✅ 找到要同步的证书目录: $cert_dir"
            SYNC_CERT_FOUND=true
            break
        fi
    done

    if [ "$SYNC_CERT_FOUND" = false ]; then
        echo "❌ 未找到包含 $DOMAIN 的证书文件，同步失败"
        return 1
    fi

    # 使用简单直接的复制方法（参考您的命令）
    echo "设置证书目录权限..."
    chmod -R 777 /etc/letsencrypt/live/

    echo "复制RSA证书文件到Windows目录..."
    cp -rf /etc/letsencrypt/live/* /mnt/e/augment/SSL/Certificates/

    # 同时复制到WSL项目目录
    echo "复制RSA证书文件到WSL项目目录..."
    mkdir -p "$HOME/projects/Certificates"
    cp -rf /etc/letsencrypt/live/* "$HOME/projects/Certificates/"

    if [ $? -eq 0 ]; then
        echo "✅ RSA证书同步完成"
        echo "  Windows目录: /mnt/e/augment/SSL/Certificates/"
        echo "  WSL目录: $HOME/projects/Certificates/"
        echo "  已复制域名: $DOMAIN (RSA 4096位)"
        return 0
    else
        echo "❌ RSA证书同步失败"
        return 1
    fi
}

# 执行证书同步
sync_to_project

#sh upload_ssl.sh
