#!/bin/bash

# Windows环境下的SSL证书申请脚本
# 解决certbot路径问题

DOMAIN="$1"
SERVER="--server https://acme.freessl.cn/v2/DV90/directory/ynn8fo1fe1j9uut8istn"

# 检查域名参数
if [ -z "$DOMAIN" ]; then
    echo "❌ 错误: 请提供域名参数"
    echo "用法: $0 <域名>"
    echo "示例: $0 example.com"
    exit 1
fi

echo "🚀 开始为域名 $DOMAIN 申请SSL证书..."

# 查找certbot的可能路径
find_certbot() {
    # 常见的certbot安装路径
    CERTBOT_PATHS=(
        "/c/Python*/Scripts/certbot.exe"
        "/c/Users/<USER>/AppData/Local/Programs/Python/Python*/Scripts/certbot.exe"
        "/c/Program Files/Python*/Scripts/certbot.exe"
        "/usr/bin/certbot"
        "/usr/local/bin/certbot"
        "certbot"
    )
    
    for path in "${CERTBOT_PATHS[@]}"; do
        # 使用通配符展开
        for expanded_path in $path; do
            if [ -f "$expanded_path" ] || command -v "$expanded_path" >/dev/null 2>&1; then
                echo "$expanded_path"
                return 0
            fi
        done
    done
    
    return 1
}

# 查找certbot
echo "正在查找certbot..."
CERTBOT_CMD=$(find_certbot)

if [ -z "$CERTBOT_CMD" ]; then
    echo "❌ 未找到certbot，尝试使用WSL中的certbot..."
    
    # 尝试使用WSL中的certbot
    if command -v wsl >/dev/null 2>&1; then
        echo "使用WSL中的certbot..."
        
        # 删除现有证书（如果存在）
        wsl sudo rm -rf "/etc/letsencrypt/live/$DOMAIN" 2>/dev/null
        
        # 申请证书
        wsl sudo certbot certonly --manual -d "*.$DOMAIN" -d "$DOMAIN" $SERVER
        
        if [ $? -eq 0 ]; then
            echo "✅ 证书申请成功"
            
            # 同步证书到Windows
            sync_certificates_from_wsl
        else
            echo "❌ 证书申请失败"
            exit 1
        fi
    else
        echo "❌ WSL不可用，无法申请证书"
        echo "请安装certbot或确保WSL环境正常"
        exit 1
    fi
else
    echo "✅ 找到certbot: $CERTBOT_CMD"
    
    # 使用找到的certbot申请证书
    apply_certificate_windows
fi

# Windows环境下申请证书
apply_certificate_windows() {
    echo "正在申请证书..."
    
    # 删除现有证书（如果存在）
    rm -rf "/c/letsencrypt/live/$DOMAIN" 2>/dev/null
    
    # 申请证书
    "$CERTBOT_CMD" certonly --manual -d "*.$DOMAIN" -d "$DOMAIN" $SERVER
    
    if [ $? -eq 0 ]; then
        echo "✅ 证书申请成功"
        sync_certificates_windows
    else
        echo "❌ 证书申请失败"
        exit 1
    fi
}

# 从WSL同步证书
sync_certificates_from_wsl() {
    echo "开始从WSL同步证书..."
    
    # 创建目标目录
    mkdir -p "/c/augment/SSL/Certificates"
    
    # 从WSL复制证书
    if wsl test -d "/etc/letsencrypt/live"; then
        wsl sudo chmod -R 755 /etc/letsencrypt/live/
        
        # 复制到Windows目录
        wsl sudo cp -rf /etc/letsencrypt/live/* "/mnt/c/augment/SSL/Certificates/" 2>/dev/null
        
        # 也复制到当前项目目录
        cp -rf "/c/augment/SSL/Certificates/"* "/c/augment/SSL/Certificates/" 2>/dev/null
        
        echo "✅ 证书已从WSL同步到Windows"
    else
        echo "❌ WSL中未找到证书目录"
    fi
}

# Windows环境下同步证书
sync_certificates_windows() {
    echo "开始同步Windows证书..."
    
    # 查找证书目录
    CERT_DIRS=(
        "/c/letsencrypt/live"
        "/c/Users/<USER>/letsencrypt/live"
        "/c/ProgramData/letsencrypt/live"
    )
    
    for cert_dir in "${CERT_DIRS[@]}"; do
        for expanded_dir in $cert_dir; do
            if [ -d "$expanded_dir" ]; then
                echo "找到证书目录: $expanded_dir"
                
                # 复制证书
                mkdir -p "/c/augment/SSL/Certificates"
                cp -rf "$expanded_dir"/* "/c/augment/SSL/Certificates/"
                
                echo "✅ 证书已同步到项目目录"
                return 0
            fi
        done
    done
    
    echo "❌ 未找到证书目录"
}

echo "证书申请脚本执行完成"
