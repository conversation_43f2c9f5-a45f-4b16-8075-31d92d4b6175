#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的SSL证书申请测试
"""

import subprocess
import os
import sys

def test_direct_script():
    """直接测试脚本"""
    print("=== 直接测试证书申请脚本 ===")
    
    # 测试脚本路径
    script_path = "../update-ssl/auto_ssl.sh"
    
    if not os.path.exists(script_path):
        print(f"❌ 脚本不存在: {script_path}")
        return False
    
    print(f"✅ 脚本存在: {script_path}")
    
    # 尝试不同的执行方式
    test_methods = [
        {
            'name': 'WSL bash',
            'command': ['wsl', 'bash', '/mnt/e/augment/SSL/update-ssl/auto_ssl.sh']
        },
        {
            'name': 'Git Bash',
            'command': ['bash', script_path]
        },
        {
            'name': 'PowerShell WSL',
            'command': ['powershell', '-c', 'wsl bash /mnt/e/augment/SSL/update-ssl/auto_ssl.sh']
        }
    ]
    
    for method in test_methods:
        print(f"\n测试方法: {method['name']}")
        try:
            result = subprocess.run(
                method['command'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            
            print(f"   返回码: {result.returncode}")
            
            if result.stdout:
                stdout_preview = result.stdout[:200].replace('\n', ' ')
                print(f"   输出: {stdout_preview}...")
            
            if result.stderr:
                stderr_preview = result.stderr[:200].replace('\n', ' ')
                print(f"   错误: {stderr_preview}...")
            
            # 检查是否有预期的错误信息（参数缺失）
            output = result.stdout + result.stderr
            if any(keyword in output for keyword in ['错误', '用法', 'Usage', 'Error', '请提供域名']):
                print(f"   ✅ {method['name']}: 脚本响应正常")
                return True
            else:
                print(f"   ❌ {method['name']}: 脚本响应异常")
                
        except subprocess.TimeoutExpired:
            print(f"   ❌ {method['name']}: 执行超时")
        except FileNotFoundError:
            print(f"   ❌ {method['name']}: 命令不存在")
        except Exception as e:
            print(f"   ❌ {method['name']}: 执行失败 - {e}")
    
    return False

def test_wsl_simple():
    """简单的WSL测试"""
    print("\n=== 简单WSL测试 ===")
    
    simple_tests = [
        {
            'name': '基本命令',
            'command': ['wsl', 'echo', 'hello']
        },
        {
            'name': '当前目录',
            'command': ['wsl', 'pwd']
        },
        {
            'name': '列出文件',
            'command': ['wsl', 'ls', '/mnt/e/augment/SSL/update-ssl/']
        },
        {
            'name': '检查bash',
            'command': ['wsl', 'which', 'bash']
        }
    ]
    
    for test in simple_tests:
        print(f"\n{test['name']}:")
        try:
            result = subprocess.run(
                test['command'],
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                print(f"   ✅ 成功: {result.stdout.strip()}")
            else:
                print(f"   ❌ 失败: {result.stderr.strip()}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def test_script_content():
    """检查脚本内容"""
    print("\n=== 检查脚本内容 ===")
    
    script_path = "../update-ssl/auto_ssl.sh"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"✅ 成功读取脚本，长度: {len(content)} 字符")
        
        # 检查关键行
        lines = content.split('\n')
        key_checks = [
            ('#!/bin/bash', 'Shebang行'),
            ('DOMAIN="$1"', '域名参数'),
            ('certbot', 'Certbot命令'),
            ('depoly_ssl', '部署函数'),
            ('sync_to_project', '同步函数')
        ]
        
        for check_text, description in key_checks:
            found = any(check_text in line for line in lines)
            if found:
                print(f"   ✅ {description}: 存在")
            else:
                print(f"   ❌ {description}: 缺失")
        
        # 显示前几行
        print("\n脚本前10行:")
        for i, line in enumerate(lines[:10], 1):
            print(f"   {i:2}: {line}")
            
    except Exception as e:
        print(f"❌ 读取脚本失败: {e}")

def test_subprocess_alternatives():
    """测试不同的subprocess调用方式"""
    print("\n=== 测试subprocess调用方式 ===")
    
    # 这是SSL管理平台中使用的调用方式
    script_path = "../update-ssl/auto_ssl.sh"
    test_domain = "test.example.com"
    
    methods = [
        {
            'name': '原始方式',
            'args': ['bash', script_path, test_domain]
        },
        {
            'name': 'WSL方式',
            'args': ['wsl', 'bash', '/mnt/e/augment/SSL/update-ssl/auto_ssl.sh', test_domain]
        },
        {
            'name': '绝对路径',
            'args': ['bash', os.path.abspath(script_path), test_domain]
        }
    ]
    
    for method in methods:
        print(f"\n测试: {method['name']}")
        print(f"命令: {' '.join(method['args'])}")
        
        try:
            # 只运行很短时间，避免实际申请证书
            result = subprocess.run(
                method['args'],
                capture_output=True,
                text=True,
                timeout=5,  # 短超时
                encoding='utf-8',
                errors='ignore'
            )
            
            print(f"   返回码: {result.returncode}")
            
            output = (result.stdout + result.stderr)[:300]
            print(f"   输出预览: {output.replace(chr(10), ' ')}")
            
        except subprocess.TimeoutExpired:
            print("   ⏰ 超时（这可能是正常的，说明脚本开始执行了）")
        except Exception as e:
            print(f"   ❌ 异常: {e}")

def main():
    """主函数"""
    print("开始简单SSL证书申请测试...\n")
    
    # 1. 检查脚本内容
    test_script_content()
    
    # 2. 简单WSL测试
    test_wsl_simple()
    
    # 3. 测试脚本执行
    test_direct_script()
    
    # 4. 测试subprocess调用方式
    test_subprocess_alternatives()
    
    print("\n" + "="*50)
    print("测试完成")
    print("="*50)
    
    print("\n💡 如果所有测试都失败，可能的原因:")
    print("1. WSL环境损坏 - 尝试: wsl --shutdown 然后重启")
    print("2. 脚本权限问题 - 在WSL中运行: chmod +x /mnt/e/augment/SSL/update-ssl/*.sh")
    print("3. 路径问题 - 检查脚本文件是否在正确位置")
    print("4. 依赖缺失 - 在WSL中安装: sudo apt install certbot expect")

if __name__ == "__main__":
    main()
