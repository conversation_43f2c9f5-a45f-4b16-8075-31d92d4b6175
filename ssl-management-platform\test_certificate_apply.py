#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试证书申请功能
"""

import requests
import json
import time
import sqlite3
import subprocess
import os

def test_wsl_environment():
    """测试WSL环境是否已修复"""
    print("=== 测试WSL环境 ===")
    
    tests = [
        ('bash版本', ['wsl', 'bash', '--version']),
        ('certbot版本', ['wsl', 'certbot', '--version']),
        ('expect检查', ['wsl', 'which', 'expect']),
        ('脚本权限', ['wsl', 'ls', '-la', '/mnt/e/augment/SSL/update-ssl/auto_ssl.sh']),
        ('脚本语法', ['wsl', 'bash', '-n', '/mnt/e/augment/SSL/update-ssl/auto_ssl.sh'])
    ]
    
    for test_name, command in tests:
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                output = result.stdout.strip()[:100]
                print(f"✅ {test_name}: {output}")
            else:
                print(f"❌ {test_name}: {result.stderr.strip()[:100]}")
        except Exception as e:
            print(f"❌ {test_name}: {e}")

def test_script_execution():
    """测试脚本执行"""
    print("\n=== 测试脚本执行 ===")
    
    test_domain = "test-example.com"
    script_path = "/mnt/e/augment/SSL/update-ssl/auto_ssl.sh"
    
    print(f"测试域名: {test_domain}")
    print(f"脚本路径: {script_path}")
    
    try:
        # 测试脚本无参数调用（应该显示用法）
        print("\n1. 测试无参数调用:")
        result = subprocess.run(['wsl', 'bash', script_path], 
                              capture_output=True, text=True, timeout=10)
        
        output = result.stdout + result.stderr
        if '错误' in output or '用法' in output:
            print("✅ 脚本参数验证正常")
            print(f"   输出: {output[:200]}")
        else:
            print("❌ 脚本参数验证异常")
            print(f"   输出: {output[:200]}")
        
        # 测试脚本有参数调用（短时间后中断）
        print(f"\n2. 测试有参数调用 ({test_domain}):")
        try:
            result = subprocess.run(['wsl', 'bash', script_path, test_domain], 
                                  capture_output=True, text=True, timeout=5)
            
            output = result.stdout + result.stderr
            print(f"   返回码: {result.returncode}")
            print(f"   输出: {output[:300]}")
            
        except subprocess.TimeoutExpired:
            print("   ⏰ 脚本开始执行（超时中断，这是正常的）")
            
    except Exception as e:
        print(f"❌ 脚本测试失败: {e}")

def test_web_certificate_apply():
    """测试Web界面证书申请"""
    print("\n=== 测试Web界面证书申请 ===")
    
    test_domain = "kimg.cn"
    
    try:
        # 1. 测试申请页面是否可访问
        print("1. 测试申请页面...")
        response = requests.get('http://localhost:5000/apply-certificate', timeout=10)
        
        if response.status_code == 200:
            print("✅ 申请页面可访问")
            
            # 检查RSA是否为默认选项
            if 'value="rsa" selected' in response.text:
                print("✅ RSA为默认选项")
            elif 'value="ecc" selected' in response.text:
                print("⚠️  ECC为默认选项")
            else:
                print("❌ 无法确定默认选项")
        else:
            print(f"❌ 申请页面不可访问: {response.status_code}")
            return False
        
        # 2. 提交证书申请
        print(f"\n2. 提交证书申请 ({test_domain})...")
        
        apply_data = {
            'domain': test_domain,
            'cert_type': 'rsa'
        }
        
        response = requests.post(
            'http://localhost:5000/apply-certificate',
            data=apply_data,
            allow_redirects=False,
            timeout=10
        )
        
        if response.status_code in [302, 303]:
            print("✅ 证书申请请求已提交")
            
            # 等待一段时间让后台任务开始
            print("   等待后台任务开始...")
            time.sleep(3)
            
            # 3. 检查数据库中的申请记录
            print("\n3. 检查申请记录...")
            check_database_records(test_domain)
            
            return True
        else:
            print(f"❌ 证书申请失败: {response.status_code}")
            print(f"   响应: {response.text[:200]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到SSL管理平台，请确保服务正在运行")
        return False
    except Exception as e:
        print(f"❌ Web申请测试失败: {e}")
        return False

def check_database_records(domain):
    """检查数据库中的证书申请记录"""
    try:
        conn = sqlite3.connect('ssl_management.db')
        cursor = conn.cursor()
        
        # 查找最近的申请记录
        cursor.execute('''
            SELECT id, domain, status, error_message, created_at, updated_at
            FROM certificates 
            WHERE domain = ? 
            ORDER BY id DESC 
            LIMIT 3
        ''', (domain,))
        
        records = cursor.fetchall()
        conn.close()
        
        if records:
            print(f"   找到 {len(records)} 条申请记录:")
            for record in records:
                cert_id, domain, status, error_msg, created_at, updated_at = record
                print(f"   ID: {cert_id}")
                print(f"   域名: {domain}")
                print(f"   状态: {status}")
                print(f"   错误信息: {error_msg or '无'}")
                print(f"   创建时间: {created_at}")
                print(f"   更新时间: {updated_at}")
                print("   " + "-" * 40)
        else:
            print("   ❌ 未找到申请记录")
            
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")

def test_backend_function():
    """直接测试后端申请函数"""
    print("\n=== 测试后端申请函数 ===")
    
    # 导入后端函数
    import sys
    sys.path.append('.')
    
    try:
        from app import apply_ssl_certificate
        
        # 创建测试记录
        conn = sqlite3.connect('ssl_management.db')
        cursor = conn.cursor()
        
        test_domain = "test-backend.example.com"
        cursor.execute(
            'INSERT INTO certificates (domain, status, error_message) VALUES (?, ?, ?)',
            (test_domain, 'applying', '测试RSA证书申请')
        )
        cert_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"创建测试记录: ID={cert_id}, 域名={test_domain}")
        
        # 调用申请函数（在新线程中，避免阻塞）
        import threading
        
        def run_apply():
            try:
                apply_ssl_certificate(cert_id, test_domain, 'rsa')
                print("✅ 后端函数执行完成")
            except Exception as e:
                print(f"❌ 后端函数执行失败: {e}")
        
        thread = threading.Thread(target=run_apply)
        thread.daemon = True
        thread.start()
        
        # 等待一段时间
        print("等待后端函数执行...")
        thread.join(timeout=10)
        
        if thread.is_alive():
            print("⏰ 后端函数仍在执行中（这是正常的）")
        
        # 检查结果
        time.sleep(2)
        check_database_records(test_domain)
        
    except Exception as e:
        print(f"❌ 后端函数测试失败: {e}")

def check_ssl_management_logs():
    """检查SSL管理平台的日志"""
    print("\n=== 检查应用日志 ===")
    
    # 检查可能的日志文件
    log_files = [
        'ssl_management.log',
        'app.log',
        '../logs/ssl_management.log',
        '../logs/app.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"✅ 找到日志文件: {log_file}")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 显示最后几行
                print("   最后10行日志:")
                for line in lines[-10:]:
                    print(f"   {line.strip()}")
                    
            except Exception as e:
                print(f"   ❌ 读取日志失败: {e}")
        else:
            print(f"❌ 日志文件不存在: {log_file}")

def main():
    """主测试函数"""
    print("开始测试证书申请功能...\n")
    
    # 1. 测试WSL环境
    test_wsl_environment()
    
    # 2. 测试脚本执行
    test_script_execution()
    
    # 3. 测试Web界面申请
    web_ok = test_web_certificate_apply()
    
    # 4. 测试后端函数
    if web_ok:
        test_backend_function()
    
    # 5. 检查日志
    check_ssl_management_logs()
    
    print("\n" + "="*50)
    print("测试完成")
    print("="*50)
    
    print("\n💡 调试建议:")
    print("1. 检查数据库中的错误信息")
    print("2. 查看应用日志文件")
    print("3. 手动执行脚本测试")
    print("4. 检查前端是否显示错误信息")
    
    print("\n🔧 如果问题持续:")
    print("1. 重启SSL管理平台服务")
    print("2. 检查脚本权限和路径")
    print("3. 验证certbot配置")
    print("4. 查看详细的subprocess输出")

if __name__ == "__main__":
    main()
