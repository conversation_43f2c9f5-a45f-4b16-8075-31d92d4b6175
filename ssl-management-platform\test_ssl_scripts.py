#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SSL证书申请脚本和环境
"""

import os
import subprocess
import sys
from pathlib import Path

def test_wsl_environment():
    """测试WSL环境"""
    print("=== 测试WSL环境 ===")
    
    try:
        # 测试WSL是否可用
        result = subprocess.run(['wsl', '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ WSL可用")
            print(f"   版本信息: {result.stdout.strip()}")
        else:
            print("❌ WSL不可用")
            print(f"   错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ WSL命令超时")
        return False
    except FileNotFoundError:
        print("❌ WSL未安装")
        return False
    except Exception as e:
        print(f"❌ WSL测试失败: {e}")
        return False
    
    # 测试WSL基本命令
    try:
        result = subprocess.run(['wsl', 'echo', 'WSL测试'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and 'WSL测试' in result.stdout:
            print("✅ WSL基本命令正常")
            return True
        else:
            print("❌ WSL基本命令失败")
            return False
            
    except Exception as e:
        print(f"❌ WSL基本命令测试失败: {e}")
        return False

def test_script_files():
    """测试脚本文件"""
    print("\n=== 测试脚本文件 ===")
    
    script_dir = Path("../update-ssl")
    scripts = ["auto_ssl.sh", "auto_rsa_ssl.sh"]
    
    results = {}
    
    for script_name in scripts:
        script_path = script_dir / script_name
        print(f"\n检查 {script_name}:")
        
        if script_path.exists():
            print(f"   ✅ 文件存在: {script_path}")
            
            # 检查文件内容
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键内容
                checks = [
                    ('#!/bin/bash', 'Shebang'),
                    ('DOMAIN="$1"', '域名参数'),
                    ('certbot', 'Certbot命令'),
                    ('acme.freessl.cn', 'ACME服务器')
                ]
                
                for check_text, description in checks:
                    if check_text in content:
                        print(f"   ✅ {description}: 存在")
                    else:
                        print(f"   ❌ {description}: 缺失")
                
                results[script_name] = True
                
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
                results[script_name] = False
        else:
            print(f"   ❌ 文件不存在: {script_path}")
            results[script_name] = False
    
    return results

def test_script_execution():
    """测试脚本执行"""
    print("\n=== 测试脚本执行 ===")
    
    # 测试通过WSL执行脚本
    script_path = "/mnt/e/augment/SSL/update-ssl/auto_ssl.sh"
    
    try:
        # 测试脚本语法检查
        print("检查ECC脚本语法...")
        result = subprocess.run([
            'wsl', 'bash', '-n', script_path
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ ECC脚本语法正确")
        else:
            print("❌ ECC脚本语法错误")
            print(f"   错误: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本语法检查超时")
    except Exception as e:
        print(f"❌ 脚本语法检查失败: {e}")
    
    try:
        # 测试脚本无参数调用（应该显示用法信息）
        print("测试ECC脚本无参数调用...")
        result = subprocess.run([
            'wsl', 'bash', script_path
        ], capture_output=True, text=True, timeout=15)
        
        output = result.stdout + result.stderr
        
        if '错误' in output or '用法' in output or 'Usage' in output:
            print("✅ ECC脚本参数验证正常")
            print(f"   输出: {output[:100]}...")
        else:
            print("❌ ECC脚本参数验证异常")
            print(f"   返回码: {result.returncode}")
            print(f"   输出: {output[:200]}...")
            
    except subprocess.TimeoutExpired:
        print("❌ 脚本执行超时")
    except Exception as e:
        print(f"❌ 脚本执行测试失败: {e}")

def test_dependencies():
    """测试依赖项"""
    print("\n=== 测试依赖项 ===")
    
    dependencies = ['bash', 'certbot', 'expect', 'openssl', 'curl']
    
    for dep in dependencies:
        try:
            result = subprocess.run([
                'wsl', 'which', dep
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ {dep}: {result.stdout.strip()}")
            else:
                print(f"❌ {dep}: 未安装")
                
        except Exception as e:
            print(f"❌ {dep}: 检查失败 - {e}")

def test_directories():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    directories = [
        "/mnt/e/augment/SSL/update-ssl",
        "/mnt/e/augment/SSL/Certificates",
        "/etc/letsencrypt"
    ]
    
    for dir_path in directories:
        try:
            result = subprocess.run([
                'wsl', 'test', '-d', dir_path
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ {dir_path}: 存在")
                
                # 检查权限
                result = subprocess.run([
                    'wsl', 'ls', '-ld', dir_path
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print(f"   权限: {result.stdout.strip()}")
                    
            else:
                print(f"❌ {dir_path}: 不存在")
                
        except Exception as e:
            print(f"❌ {dir_path}: 检查失败 - {e}")

def main():
    """主测试函数"""
    print("开始SSL证书申请环境测试...\n")
    
    # 测试结果
    tests = []
    
    # 1. 测试WSL环境
    wsl_ok = test_wsl_environment()
    tests.append(('WSL环境', wsl_ok))
    
    if not wsl_ok:
        print("\n❌ WSL环境不可用，无法继续测试")
        return
    
    # 2. 测试脚本文件
    script_results = test_script_files()
    tests.append(('脚本文件', all(script_results.values())))
    
    # 3. 测试脚本执行
    test_script_execution()
    tests.append(('脚本执行', True))  # 这个测试主要是观察输出
    
    # 4. 测试依赖项
    test_dependencies()
    tests.append(('依赖项', True))  # 这个测试主要是观察输出
    
    # 5. 测试目录结构
    test_directories()
    tests.append(('目录结构', True))  # 这个测试主要是观察输出
    
    # 输出测试总结
    print("\n" + "="*50)
    print("测试总结:")
    print("="*50)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:15} {status}")
    
    print("\n💡 问题排查建议:")
    print("1. 如果WSL不可用:")
    print("   - 检查WSL是否正确安装")
    print("   - 尝试重启WSL: wsl --shutdown")
    print("   - 检查Windows功能中的WSL是否启用")
    
    print("\n2. 如果脚本执行失败:")
    print("   - 检查脚本权限: chmod +x *.sh")
    print("   - 检查脚本语法: bash -n script.sh")
    print("   - 检查依赖项是否安装")
    
    print("\n3. 如果依赖项缺失:")
    print("   - 更新包列表: sudo apt update")
    print("   - 安装certbot: sudo apt install certbot")
    print("   - 安装expect: sudo apt install expect")

if __name__ == "__main__":
    main()
