#!/bin/bash

# SSL证书申请包装脚本
# 解决路径和环境问题

DOMAIN="$1"

if [ -z "$DOMAIN" ]; then
    echo "❌ 错误: 请提供域名参数"
    echo "用法: $0 <域名>"
    echo "示例: $0 example.com"
    exit 1
fi

echo "🚀 开始为域名 $DOMAIN 申请SSL证书..."

# 设置服务器
SERVER="--server https://acme.freessl.cn/v2/DV90/directory/ynn8fo1fe1j9uut8istn"

# 申请证书函数
apply_certificate() {
    echo "正在申请证书..."
    
    # 检查certbot是否可用
    if ! command -v certbot >/dev/null 2>&1; then
        echo "❌ certbot未安装，请先安装: sudo apt install certbot"
        exit 1
    fi
    
    # 删除现有证书（如果存在）
    if [ -d "/etc/letsencrypt/live/$DOMAIN" ]; then
        echo "删除现有证书: $DOMAIN"
        sudo rm -rf "/etc/letsencrypt/live/$DOMAIN"
    fi
    
    # 申请证书
    certbot certonly --manual -d "*.$DOMAIN" -d "$DOMAIN" $SERVER
    
    if [ $? -eq 0 ]; then
        echo "✅ 证书申请成功"
        
        # 设置权限
        sudo chmod -R 755 /etc/letsencrypt/live/
        
        # 同步证书
        sync_certificates
    else
        echo "❌ 证书申请失败"
        exit 1
    fi
}

# 同步证书函数
sync_certificates() {
    echo "开始同步证书..."
    
    # 创建目标目录
    mkdir -p "/tmp/certificates"
    
    # 复制证书
    if [ -d "/etc/letsencrypt/live" ]; then
        sudo cp -rf /etc/letsencrypt/live/* "/tmp/certificates/"
        sudo chmod -R 777 "/tmp/certificates"
        echo "✅ 证书已同步到 /tmp/certificates"
    else
        echo "❌ 证书目录不存在"
    fi
}

# 执行申请
apply_certificate
